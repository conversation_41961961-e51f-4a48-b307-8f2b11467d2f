<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Challenge</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .quiz-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 100%;
            color: #333;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
        }

        .quiz-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .quiz-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .quiz-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .quiz-description {
            color: #666;
            font-size: 16px;
        }

        .coming-soon {
            text-align: center;
            padding: 40px 20px;
        }

        .coming-soon h2 {
            color: #ff6b9d;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .coming-soon p {
            color: #666;
            font-size: 18px;
            margin-bottom: 30px;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .quiz-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <div class="quiz-icon" id="quizIcon">🎯</div>
            <div class="quiz-title" id="quizTitle">Quiz Challenge</div>
            <div class="quiz-description" id="quizDescription">准备开始挑战吧！</div>
        </div>

        <div class="quiz-info" id="quizInfo">
            <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value">🟢 Live</span>
            </div>
            <div class="info-item">
                <span class="info-label">参与费用:</span>
                <span class="info-value">💰 Free</span>
            </div>
            <div class="info-item">
                <span class="info-label">奖金池:</span>
                <span class="info-value" id="prizeMoney">🪙 0</span>
            </div>
        </div>

        <div class="coming-soon">
            <h2>🚧 Coming Soon</h2>
            <p>题目页面正在开发中，敬请期待！</p>
            <a href="index.html" class="back-button">🏠 返回首页</a>
        </div>
    </div>

    <script src="questions.js"></script>
    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            const quizId = getUrlParameter('quiz');
            
            if (quizId) {
                const quizInfo = getQuizInfo(quizId);
                
                if (quizInfo) {
                    document.getElementById('quizIcon').textContent = quizInfo.icon;
                    document.getElementById('quizTitle').textContent = quizInfo.title;
                    document.getElementById('quizDescription').textContent = quizInfo.description;
                    document.getElementById('prizeMoney').textContent = `🪙 ${quizInfo.prize}`;
                    document.title = `${quizInfo.title} - Quiz Challenge`;
                }
            }
        });
    </script>
</body>
</html>
