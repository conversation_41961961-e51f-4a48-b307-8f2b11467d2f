<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Challenge - QuizMaster Pro</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/quiz.css">


</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <img src="images/logo.svg" alt="QuizMaster Pro">
                QuizMaster Pro
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="about.html">About</a></li>
                    <li><a href="privacy.html">Privacy Policy</a></li>
                    <li><a href="terms.html">Terms of Service</a></li>
                </ul>
            </nav>
            <div class="user-points" id="userPoints">
                <span class="points-icon">💎</span>
                <span id="pointsValue">0</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="quiz-container">
        <div class="quiz-header">
            <div class="quiz-icon" id="quizIcon">🎯</div>
            <div class="quiz-title" id="quizTitle">Quiz Challenge</div>
            <div class="quiz-description" id="quizDescription">准备开始挑战吧！</div>
        </div>

        <div class="quiz-info" id="quizInfo" style="display: none;">
            <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value">🟢 Live</span>
            </div>
            <div class="info-item">
                <span class="info-label">参与费用:</span>
                <span class="info-value">💰 Free</span>
            </div>
            <div class="info-item">
                <span class="info-label">奖金池:</span>
                <span class="info-value" id="prizeMoney">🪙 0</span>
            </div>
        </div>

        <!-- Loading State -->
        <div class="loading" id="loadingState">
            <h2>🔄 Loading...</h2>
            <p>Preparing your quiz questions, please wait...</p>
        </div>

        <!-- Quiz Interface -->
        <div id="quizInterface" style="display: none;">
            <div class="quiz-progress">
                <span id="questionNumber">1/10</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 10%"></div>
                </div>
                <span id="timeRemaining">⏱️ 30s</span>
            </div>

            <div class="question-container">
                <div class="question-text" id="questionText">
                    Loading question...
                </div>
                <div class="options-container" id="optionsContainer">
                    <!-- Options will be dynamically generated by JavaScript -->
                </div>
            </div>

            <div class="quiz-actions">
                <button class="action-button next-button" id="nextButton" onclick="nextQuestion()" disabled>
                    Next Question
                </button>
            </div>
        </div>

        <!-- Results Page -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <div class="score-display" id="finalScore">0/10</div>
            <div class="score-text" id="scoreText">Congratulations on completing the challenge!</div>
            <div class="quiz-actions">
                <button class="action-button back-button" onclick="restartQuiz()">
                    🔄 Try Again
                </button>
                <a href="index.html" class="action-button back-button" style="text-decoration: none; display: flex; align-items: center; justify-content: center;">
                    🏠 Back to Home
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="about.html">About Us</a>
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
                <a href="#contact">Contact</a>
            </div>
            <p class="footer-text">© 2024 QuizMaster Pro. All rights reserved. Challenge yourself, learn something new!</p>
        </div>
    </footer>

    <script src="questions.js"></script>
    <script>
        // Global variables
        let currentQuiz = null;
        let questions = [];
        let currentQuestionIndex = 0;
        let selectedAnswer = null;
        let score = 0;
        let timeLeft = 30;
        let timer = null;
        let isAnswered = false;
        let pointsEarned = 0;

        // Points System
        function getUserPoints() {
            return parseInt(localStorage.getItem('userPoints') || '0');
        }

        function setUserPoints(points) {
            localStorage.setItem('userPoints', points.toString());
            updatePointsDisplay();
        }

        function addPoints(points) {
            const currentPoints = getUserPoints();
            setUserPoints(currentPoints + points);
            pointsEarned += points;
        }

        function updatePointsDisplay() {
            const pointsElement = document.getElementById('pointsValue');
            if (pointsElement) {
                pointsElement.textContent = getUserPoints().toLocaleString();
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Get URL parameter
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Initialize page
        async function initializePage() {
            const quizId = getUrlParameter('quiz');

            if (!quizId) {
                window.location.href = 'index.html';
                return;
            }

            // Wait for data loading
            await loadQuestionBank();

            currentQuiz = getQuizInfo(quizId);
            if (!currentQuiz) {
                alert('Quiz not found!');
                window.location.href = 'index.html';
                return;
            }

            // Set page information
            document.getElementById('quizIcon').textContent = currentQuiz.icon;
            document.getElementById('quizTitle').textContent = currentQuiz.title;
            document.getElementById('quizDescription').textContent = currentQuiz.description;
            document.getElementById('prizeMoney').textContent = `🏆 ${currentQuiz.prize} pts`;
            document.title = `${currentQuiz.title} - QuizMaster Pro`;

            // Get questions
            questions = getRandomQuestions(quizId, 10);

            if (questions.length === 0) {
                alert('No questions available for this quiz!');
                window.location.href = 'index.html';
                return;
            }

            // Initialize points display
            updatePointsDisplay();
            pointsEarned = 0;

            // Start quiz
            setTimeout(startQuiz, 1500);
        }

        // Start quiz
        function startQuiz() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            currentQuestionIndex = 0;
            score = 0;
            showQuestion();
        }

        // Show question
        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                showResults();
                return;
            }

            const question = questions[currentQuestionIndex];
            isAnswered = false;
            selectedAnswer = null;
            timeLeft = 30;

            // Update progress
            document.getElementById('questionNumber').textContent = `${currentQuestionIndex + 1}/${questions.length}`;
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;

            // Show question
            document.getElementById('questionText').textContent = question.question;

            // Generate options
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-button';
                button.textContent = option;
                button.onclick = () => selectAnswer(index);
                optionsContainer.appendChild(button);
            });

            // Reset button state
            document.getElementById('nextButton').disabled = true;
            const nextBtn = document.getElementById('nextButton');
            nextBtn.textContent = currentQuestionIndex === questions.length - 1 ? 'View Results' : 'Next Question';

            // Start timer
            startTimer();
        }

        // Start timer
        function startTimer() {
            if (timer) clearInterval(timer);

            timer = setInterval(() => {
                if (!isAnswered) {  // Only decrease time if not answered
                    timeLeft--;
                    document.getElementById('timeRemaining').textContent = `⏱️ ${timeLeft}s`;

                    if (timeLeft <= 0) {
                        clearInterval(timer);
                        // Time's up, auto-select wrong answer
                        selectAnswer(-1);
                    }
                }
            }, 1000);
        }

        // Select answer
        function selectAnswer(answerIndex) {
            if (isAnswered) return;

            isAnswered = true;
            selectedAnswer = answerIndex;
            clearInterval(timer); // Stop timer when answer is selected

            const question = questions[currentQuestionIndex];
            const options = document.querySelectorAll('.option-button');

            // Show correct answer
            options.forEach((option, index) => {
                option.onclick = null; // Disable clicking

                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === selectedAnswer && index !== question.correct) {
                    option.classList.add('incorrect');
                } else if (index === selectedAnswer) {
                    option.classList.add('selected');
                }
            });

            // Check answer and award points
            if (selectedAnswer === question.correct) {
                score++;
                // Award points based on time remaining (bonus for quick answers)
                const timeBonus = Math.max(1, Math.floor(timeLeft / 5));
                const pointsForQuestion = 100 + (timeBonus * 10);
                addPoints(pointsForQuestion);
            }

            // Enable next button
            document.getElementById('nextButton').disabled = false;
        }

        // Next question
        function nextQuestion() {
            currentQuestionIndex++;
            showQuestion();
        }

        // Show results
        function showResults() {
            document.getElementById('quizInterface').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const percentage = Math.round((score / questions.length) * 100);
            document.getElementById('finalScore').textContent = `${score}/${questions.length}`;

            let message = '';
            if (percentage >= 90) {
                message = '🏆 Perfect Performance! You are a true expert!';
            } else if (percentage >= 70) {
                message = '🎉 Excellent work! Keep it up!';
            } else if (percentage >= 50) {
                message = '👍 Good job! There\'s room for improvement!';
            } else {
                message = '💪 Keep trying! Practice makes perfect!';
            }

            document.getElementById('scoreText').textContent = message;

            // Show points earned
            if (pointsEarned > 0) {
                const pointsDisplay = document.createElement('div');
                pointsDisplay.className = 'points-earned';
                pointsDisplay.textContent = `+${pointsEarned} points earned!`;
                document.getElementById('scoreText').appendChild(pointsDisplay);

                showNotification(`Congratulations! You earned ${pointsEarned} points!`, 'success');
            }
        }

        // Restart quiz
        function restartQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswer = null;
            isAnswered = false;
            pointsEarned = 0;

            // Get new random questions
            const quizId = getUrlParameter('quiz');
            questions = getRandomQuestions(quizId, 10);

            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            showQuestion();
        }

        // Initialize page when loaded
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
