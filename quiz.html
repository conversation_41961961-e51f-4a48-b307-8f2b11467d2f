<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Challenge</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .quiz-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 100%;
            color: #333;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
        }

        .quiz-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .quiz-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .quiz-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .quiz-description {
            color: #666;
            font-size: 16px;
        }

        .quiz-progress {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 0 15px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .question-container {
            margin-bottom: 30px;
        }

        .question-text {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            line-height: 1.5;
        }

        .options-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .option-button {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #333;
        }

        .option-button:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .option-button.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option-button.correct {
            border-color: #28a745;
            background: #d4edda;
            color: #155724;
        }

        .option-button.incorrect {
            border-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .quiz-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .action-button {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .next-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .next-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .next-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-container {
            text-align: center;
            padding: 30px 20px;
        }

        .score-display {
            font-size: 48px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }

        .score-text {
            font-size: 24px;
            margin-bottom: 30px;
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .quiz-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <div class="quiz-icon" id="quizIcon">🎯</div>
            <div class="quiz-title" id="quizTitle">Quiz Challenge</div>
            <div class="quiz-description" id="quizDescription">准备开始挑战吧！</div>
        </div>

        <div class="quiz-info" id="quizInfo" style="display: none;">
            <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value">🟢 Live</span>
            </div>
            <div class="info-item">
                <span class="info-label">参与费用:</span>
                <span class="info-value">💰 Free</span>
            </div>
            <div class="info-item">
                <span class="info-label">奖金池:</span>
                <span class="info-value" id="prizeMoney">🪙 0</span>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loadingState">
            <h2>🔄 加载中...</h2>
            <p>正在准备题目，请稍候...</p>
        </div>

        <!-- 答题界面 -->
        <div id="quizInterface" style="display: none;">
            <div class="quiz-progress">
                <span id="questionNumber">1/10</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 10%"></div>
                </div>
                <span id="timeRemaining">⏱️ 30s</span>
            </div>

            <div class="question-container">
                <div class="question-text" id="questionText">
                    题目加载中...
                </div>
                <div class="options-container" id="optionsContainer">
                    <!-- 选项将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="quiz-actions">
                <button class="action-button next-button" id="nextButton" onclick="nextQuestion()" disabled>
                    下一题
                </button>
            </div>
        </div>

        <!-- 结果页面 -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <div class="score-display" id="finalScore">0/10</div>
            <div class="score-text" id="scoreText">恭喜完成挑战！</div>
            <div class="quiz-actions">
                <button class="action-button back-button" onclick="restartQuiz()">
                    🔄 重新挑战
                </button>
                <a href="index.html" class="action-button back-button" style="text-decoration: none; display: flex; align-items: center; justify-content: center;">
                    🏠 返回首页
                </a>
            </div>
        </div>
    </div>

    <script src="questions.js"></script>
    <script>
        // 全局变量
        let currentQuiz = null;
        let questions = [];
        let currentQuestionIndex = 0;
        let selectedAnswer = null;
        let score = 0;
        let timeLeft = 30;
        let timer = null;
        let isAnswered = false;

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化页面
        async function initializePage() {
            const quizId = getUrlParameter('quiz');

            if (!quizId) {
                window.location.href = 'index.html';
                return;
            }

            // 等待数据加载
            await loadQuestionBank();

            currentQuiz = getQuizInfo(quizId);
            if (!currentQuiz) {
                alert('题库不存在！');
                window.location.href = 'index.html';
                return;
            }

            // 设置页面信息
            document.getElementById('quizIcon').textContent = currentQuiz.icon;
            document.getElementById('quizTitle').textContent = currentQuiz.title;
            document.getElementById('quizDescription').textContent = currentQuiz.description;
            document.getElementById('prizeMoney').textContent = `🪙 ${currentQuiz.prize}`;
            document.title = `${currentQuiz.title} - Quiz Challenge`;

            // 获取题目
            questions = getRandomQuestions(quizId, 10);

            if (questions.length === 0) {
                alert('该题库暂无题目！');
                window.location.href = 'index.html';
                return;
            }

            // 开始答题
            setTimeout(startQuiz, 1500);
        }

        // 开始答题
        function startQuiz() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            currentQuestionIndex = 0;
            score = 0;
            showQuestion();
        }

        // 显示题目
        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                showResults();
                return;
            }

            const question = questions[currentQuestionIndex];
            isAnswered = false;
            selectedAnswer = null;
            timeLeft = 30;

            // 更新进度
            document.getElementById('questionNumber').textContent = `${currentQuestionIndex + 1}/${questions.length}`;
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;

            // 显示题目
            document.getElementById('questionText').textContent = question.question;

            // 生成选项
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-button';
                button.textContent = option;
                button.onclick = () => selectAnswer(index);
                optionsContainer.appendChild(button);
            });

            // 重置按钮状态
            document.getElementById('nextButton').disabled = true;
            document.getElementById('nextButton').textContent = '下一题';

            // 开始计时
            startTimer();
        }

        // 开始计时器
        function startTimer() {
            if (timer) clearInterval(timer);

            timer = setInterval(() => {
                timeLeft--;
                document.getElementById('timeRemaining').textContent = `⏱️ ${timeLeft}s`;

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    if (!isAnswered) {
                        // 时间到，自动选择错误答案
                        selectAnswer(-1);
                    }
                }
            }, 1000);
        }

        // 选择答案
        function selectAnswer(answerIndex) {
            if (isAnswered) return;

            isAnswered = true;
            selectedAnswer = answerIndex;
            clearInterval(timer);

            const question = questions[currentQuestionIndex];
            const options = document.querySelectorAll('.option-button');

            // 显示正确答案
            options.forEach((option, index) => {
                option.onclick = null; // 禁用点击

                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === selectedAnswer) {
                    option.classList.add('incorrect');
                }
            });

            // 检查答案
            if (selectedAnswer === question.correct) {
                score++;
            }

            // 启用下一题按钮
            document.getElementById('nextButton').disabled = false;

            // 如果是最后一题，改变按钮文字
            if (currentQuestionIndex === questions.length - 1) {
                document.getElementById('nextButton').textContent = '查看结果';
            }
        }

        // 下一题
        function nextQuestion() {
            currentQuestionIndex++;
            showQuestion();
        }

        // 显示结果
        function showResults() {
            document.getElementById('quizInterface').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const percentage = Math.round((score / questions.length) * 100);
            document.getElementById('finalScore').textContent = `${score}/${questions.length}`;

            let message = '';
            if (percentage >= 90) {
                message = '🏆 完美表现！你是真正的专家！';
            } else if (percentage >= 70) {
                message = '🎉 表现优秀！继续保持！';
            } else if (percentage >= 50) {
                message = '👍 不错的成绩！还有提升空间！';
            } else {
                message = '💪 继续努力！多练习会更好！';
            }

            document.getElementById('scoreText').textContent = message;
        }

        // 重新开始
        function restartQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswer = null;
            isAnswered = false;

            // 重新获取随机题目
            const quizId = getUrlParameter('quiz');
            questions = getRandomQuestions(quizId, 10);

            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            showQuestion();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
