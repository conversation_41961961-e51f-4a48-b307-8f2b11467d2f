{"General Knowledge": [{"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Who invented the first ever chocolate bar, in 1847?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What kind of aircraft was developed by <PERSON> in the United States in 1942?", "correct_answer": "Helicopter", "incorrect_answers": ["Stealth Blimp", "Jet", "Space Capsule"], "all_answers": ["Helicopter", "Stealth Blimp", "Jet", "Space Capsule"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What airline was the owner of the plane that crashed off the coast of Nova Scotia in 1998?", "correct_answer": "Swiss Air", "incorrect_answers": ["Air France", "British Airways", "TWA"], "all_answers": ["Air France", "Swiss Air", "British Airways", "TWA"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "In aerodynamics, which force pushes an object upwards?", "correct_answer": "Lift", "incorrect_answers": ["Drag", "Weight", "Thrust"], "all_answers": ["Drag", "Weight", "Lift", "Thrust"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of the following is the IATA code for Manchester Airport?", "correct_answer": "MAN", "incorrect_answers": ["EGLL", "LHR", "EGCC"], "all_answers": ["EGLL", "LHR", "EGCC", "MAN"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which American-owned brewery led the country in sales by volume in 2015?", "correct_answer": "D. G. Yuengling and Son, Inc", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Boston Beer Company", "Miller Coors"], "all_answers": ["<PERSON><PERSON><PERSON>", "Boston Beer Company", "D. G. Yuengling and Son, Inc", "Miller Coors"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of these holidays is NOT usually celebrated in the month of December?", "correct_answer": "Thanksgiving", "incorrect_answers": ["Christmas", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["Christmas", "<PERSON><PERSON><PERSON><PERSON>", "Thanksgiving", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What style of beer will typically have a higher than average hop content?", "correct_answer": "India Pale Ale", "incorrect_answers": ["Stout", "Extra Special Bitter", "Scotch Ale"], "all_answers": ["India Pale Ale", "Stout", "Extra Special Bitter", "Scotch Ale"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "The drug cartel run by <PERSON> originated in which South American city?", "correct_answer": "Medell&iacute;n", "incorrect_answers": ["Bogot&aacute;", "Quito", "Cali"], "all_answers": ["Bogot&aacute;", "Quito", "Medell&iacute;n", "Cali"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What is the first book of the Old Testament?", "correct_answer": "Genesis", "incorrect_answers": ["Exodus", "Leviticus", "Numbers"], "all_answers": ["Genesis", "Exodus", "Leviticus", "Numbers"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which sign of the zodiac is represented by the Crab?", "correct_answer": "Cancer", "incorrect_answers": ["Libra", "Virgo", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["Libra", "Cancer", "Virgo", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "The &ldquo;fairy&rdquo; type made it&rsquo;s debut in which generation of the Pokemon core series games?", "correct_answer": "6th", "incorrect_answers": ["2nd", "7th", "4th"], "all_answers": ["2nd", "6th", "7th", "4th"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "In which fast food chain can you order a Jamocha Shake?", "correct_answer": "Arby&#039;s", "incorrect_answers": ["McDonald&#039;s", "Burger King", "Wendy&#039;s"], "all_answers": ["McDonald&#039;s", "Burger King", "Arby&#039;s", "Wendy&#039;s"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What company developed the vocaloid Hat<PERSON><PERSON> Miku?", "correct_answer": "Crypton Future Media", "incorrect_answers": ["Sega", "Sony", "Yamaha Corporation"], "all_answers": ["Sega", "Sony", "Crypton Future Media", "Yamaha Corporation"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "What word represents the letter &#039;T&#039; in the NATO phonetic alphabet?", "correct_answer": "Tango", "incorrect_answers": ["Target", "Taxi", "Turkey"], "all_answers": ["Tango", "Target", "Taxi", "Turkey"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which American president appears on a one dollar bill?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which candy is NOT made by Mars?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["M&amp;M&#039;s", "<PERSON><PERSON><PERSON>", "Snickers"], "all_answers": ["<PERSON><PERSON>", "M&amp;M&#039;s", "<PERSON><PERSON><PERSON>", "Snickers"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which of the following card games revolves around numbers and basic math?", "correct_answer": "Uno", "incorrect_answers": ["Go Fish", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["Go Fish", "Uno", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "Which canal connects the Mediterranean Sea with the Red Sea?", "correct_answer": "Suez Canal", "incorrect_answers": ["Panama Canal", "Sinai Canal", "Qaraqum Canal"], "all_answers": ["Panama Canal", "Sinai Canal", "Qaraqum Canal", "Suez Canal"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "General Knowledge", "question": "On a dartboard, what number is directly opposite No. 1?", "correct_answer": "19", "incorrect_answers": ["20", "12", "15"], "all_answers": ["20", "12", "15", "19"], "correct_answer_index": 3}], "Entertainment: Books": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "In the novel 1984, written by <PERSON>, what is the name of the totalitarian regime that controls Oceania?", "correct_answer": "INGSOC", "incorrect_answers": ["Neo-Bolshevism", "Obliteration of the Self", "Earth Alliance"], "all_answers": ["Neo-Bolshevism", "Obliteration of the Self", "INGSOC", "Earth Alliance"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the title of the first Sherlock Holmes book by <PERSON>?", "correct_answer": "A Study in Scarlet", "incorrect_answers": ["The Sign of the Four", "A Case of Identity", "The Doings of <PERSON><PERSON>w"], "all_answers": ["A Study in Scarlet", "The Sign of the Four", "A Case of Identity", "The Doings of <PERSON><PERSON>w"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Under what pseudonym did <PERSON> publish five novels between 1977 and 1984?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which of the following is the world&#039;s best-selling book?", "correct_answer": "The Lord of the Rings", "incorrect_answers": ["The Little Prince", "<PERSON> and the Philosopher&#039;s <PERSON>", "The Da Vinci Code"], "all_answers": ["The Lord of the Rings", "The Little Prince", "<PERSON> and the Philosopher&#039;s <PERSON>", "The Da Vinci Code"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which famous book is sub-titled &#039;The Modern Prometheus&#039;?", "correct_answer": "Frankenstein", "incorrect_answers": ["Dracula", "The Strange Case of Dr<PERSON> and Mr. <PERSON> ", "The Legend of Sleepy Hollow"], "all_answers": ["Frankenstein", "Dracula", "The Strange Case of Dr<PERSON> and Mr. <PERSON> ", "The Legend of Sleepy Hollow"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What&#039;s the second book in <PERSON> <PERSON><PERSON>&#039;s &#039;A Song of Ice and Fire&#039; series?", "correct_answer": "A Clash of Kings", "incorrect_answers": ["A Dance with Dragons", "A Storm of Swords", "A Feast for Crows"], "all_answers": ["A Dance with Dragons", "A Clash of Kings", "A Storm of Swords", "A Feast for Crows"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which classic book opens with the line &quot;Call me <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["A Tale of Two Cities", "Kidnapped", "Wuthering Heights"], "all_answers": ["A Tale of Two Cities", "<PERSON><PERSON>", "Kidnapped", "Wuthering Heights"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What&#039;s <PERSON>&#039;s dad&#039;s name?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Sr."], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Sr."], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who wrote the young adult novel &quot;The Fault in Our Stars&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which is NOT a book in the Harry Potter Series?", "correct_answer": "The House Elf", "incorrect_answers": ["The Chamber of Secrets", "The Prisoner of Azkaban", "The Deathly Hallows"], "all_answers": ["The Chamber of Secrets", "The Prisoner of Azkaban", "The Deathly Hallows", "The House Elf"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What was Sir <PERSON>&#039;s original name in &quot;The Railway Series&quot; and it&#039;s animated counterpart &quot;Thomas and Friends?&quot;", "correct_answer": "Falcon", "incorrect_answers": ["Eagle", "Kyte", "S<PERSON>ow"], "all_answers": ["Eagle", "Falcon", "Kyte", "S<PERSON>ow"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the name of the protagonist of <PERSON><PERSON><PERSON><PERSON>&#039;s novel Catcher in the Rye?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Which famous spy novelist wrote the childrens&#039; story &quot;<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>-<PERSON>&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "In what <PERSON> book was a Horcrux first encountered?", "correct_answer": "The Chamber of Secrets", "incorrect_answers": ["The Half-Blood Prince", "The Goblet of Fire", "The Deathly Hallows"], "all_answers": ["The Half-Blood Prince", "The Goblet of Fire", "The Deathly Hallows", "The Chamber of Secrets"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "&quot;Green Eggs And Ham&quot; is a book by which author?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What was the first ever entry written for the SCP Foundation collaborative writing project?", "correct_answer": "SCP-173", "incorrect_answers": ["SCP-001", "SCP-999", "SCP-1459"], "all_answers": ["SCP-173", "SCP-001", "SCP-999", "SCP-1459"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "How many Harry Potter books are there?", "correct_answer": "7", "incorrect_answers": ["8", "5", "6"], "all_answers": ["8", "7", "5", "6"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "What is the name of the three headed dog in <PERSON> and the Sorcerer&#039;s <PERSON>?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Spike", "Poofy", "Spot"], "all_answers": ["Spike", "Poofy", "Spot", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "Who was the author of the 1954 novel, &quot;Lord of the Flies&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Books", "question": "<PERSON> wrote this book, which is often considered a statement on government oversight.", "correct_answer": "1984", "incorrect_answers": ["The Old Man and the Sea", "Catcher and the Rye", "To Kill a Mockingbird"], "all_answers": ["The Old Man and the Sea", "Catcher and the Rye", "To Kill a Mockingbird", "1984"], "correct_answer_index": 3}], "Entertainment: Film": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which animated movie was first to feature a celebrity as a voice actor?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Toy Story", "<PERSON> and the Giant Peach", "The Hunchback of Notre Dame"], "all_answers": ["Toy Story", "<PERSON> and the Giant Peach", "<PERSON><PERSON><PERSON>", "The Hunchback of Notre Dame"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "&quot;The first rule is: you don&#039;t talk about it&quot; is a reference to which movie?", "correct_answer": "Fight Club", "incorrect_answers": ["The Island", "Unthinkable", "American Pie"], "all_answers": ["The Island", "Fight Club", "Unthinkable", "American Pie"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "This movie contains the quote, &quot;Houston, we have a problem.&quot;", "correct_answer": "Apollo 13", "incorrect_answers": ["The Right Stuff", "Capricorn One", "Marooned"], "all_answers": ["The Right Stuff", "Apollo 13", "Capricorn One", "Marooned"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who plays <PERSON> in the movie &quot;Big Trouble in Little China?&quot;", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What was <PERSON>&#039;s surname in &#039;The Wizard Of Oz&#039;?", "correct_answer": "Gale", "incorrect_answers": ["<PERSON>", "Day", "<PERSON>"], "all_answers": ["<PERSON>", "Gale", "Day", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "What breed of dog was <PERSON> in the film &quot;Marley &amp; Me&quot; (2008)?", "correct_answer": "Labrador Retriever", "incorrect_answers": ["Golden Retriever", "<PERSON><PERSON><PERSON>", "Shiba Inu"], "all_answers": ["Golden Retriever", "Labrador Retriever", "<PERSON><PERSON><PERSON>", "Shiba Inu"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "This movie contains the quote, &quot;<PERSON> puts <PERSON> in a corner.&quot;", "correct_answer": "Dirty Dancing", "incorrect_answers": ["Three Men and a Baby", "Ferris Bueller&#039;s Day Off", "Pretty in Pink"], "all_answers": ["Three Men and a Baby", "Dirty Dancing", "Ferris Bueller&#039;s Day Off", "Pretty in Pink"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which <PERSON> film had the theme song written and performed by English singer-songwriter <PERSON>?", "correct_answer": "Skyfall", "incorrect_answers": ["Casino Royale", "Quantum Solace", "<PERSON><PERSON><PERSON>"], "all_answers": ["Casino Royale", "Quantum Solace", "Skyfall", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of these actors does NOT appear in the 1998 movie &quot;Saving Private Ryan&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Vin Diesel", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "Vin Diesel", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who wrote and directed the 1986 film &#039;Platoon&#039;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "The Queen song `A Kind Of Magic` is featured in which 1986 film?", "correct_answer": "Highlander", "incorrect_answers": ["<PERSON>", "Labyrinth", "<PERSON> the Duck"], "all_answers": ["Highlander", "<PERSON>", "Labyrinth", "<PERSON> the Duck"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "In the 1992 film &quot;Army of Darkness&quot;, what name does <PERSON> give to his double-barreled shotgun?", "correct_answer": "Boomstick", "incorrect_answers": ["Bloomstick", "Blastbranch", "2-<PERSON><PERSON>"], "all_answers": ["Bloomstick", "Blastbranch", "Boomstick", "2-<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who played <PERSON><PERSON> in Avengers: Infinity War and Avengers: End Game?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "When was the movie &#039;Con Air&#039; released?", "correct_answer": "1997", "incorrect_answers": ["1985", "1999", "1990"], "all_answers": ["1985", "1999", "1997", "1990"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Who played Deputy Marshal <PERSON> in the 1993 film &quot;The Fugitive&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "In the Lord of the Rings: The Two Towers, where are the party of orcs taking the hobbits?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "Mordor", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mordor", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of these Movies was NOT released in 1996?", "correct_answer": "Gladiator", "incorrect_answers": ["Independence Day", "The Rock", "Mission: Impossible"], "all_answers": ["Gladiator", "Independence Day", "The Rock", "Mission: Impossible"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "In the Super Mario Bros. Movie (2023), who plays <PERSON><PERSON>?", "correct_answer": "<PERSON><PERSON>-<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "In &quot;Mean Girls&quot;, who has breasts that tell when it&#039;s raining?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Film", "question": "Which of these films is NOT set in Los Angeles?", "correct_answer": "RoboCop", "incorrect_answers": ["Blade Runner", "The Terminator", "Predator 2"], "all_answers": ["RoboCop", "Blade Runner", "The Terminator", "Predator 2"], "correct_answer_index": 0}], "Entertainment: Music": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What album did The Lumineers release in 2016?", "correct_answer": "Cleopatra", "incorrect_answers": ["Winter", "The Lumineers", "Tracks From The Attic"], "all_answers": ["Cleopatra", "Winter", "The Lumineers", "Tracks From The Attic"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "From which country did the song &quot;Gangnam Style&quot; originate from?", "correct_answer": "South Korea", "incorrect_answers": ["Japan", "North Korea", "China"], "all_answers": ["Japan", "North Korea", "China", "South Korea"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which English guitarist has the nickname &quot;Slowhand&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "The four strings on a violin are the G string, D string, A string, and...", "correct_answer": "E string", "incorrect_answers": ["C string", "B string", "F string"], "all_answers": ["C string", "E string", "B string", "F string"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Who is the lead singer of Pearl Jam?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "Stone Gossard", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "Stone Gossard", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was the best selling album of 2015?", "correct_answer": "<PERSON>, 25", "incorrect_answers": ["<PERSON><PERSON>, <PERSON><PERSON>", "<PERSON>, 1989", "<PERSON>, <PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>, 25", "<PERSON><PERSON>, <PERSON><PERSON>", "<PERSON>, 1989", "<PERSON>, <PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "According to a song by <PERSON>, Heaven is a place on what?", "correct_answer": "Earth", "incorrect_answers": ["Venus", "Mars", "Uranus"], "all_answers": ["Venus", "Mars", "Uranus", "Earth"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which of these are NOT a Men at Work song?", "correct_answer": "Basket Case", "incorrect_answers": ["Dr. <PERSON> and Mr. <PERSON><PERSON>", "Who Can It Be Now?", "<PERSON> Good Johnny"], "all_answers": ["Basket Case", "Dr. <PERSON> and Mr. <PERSON><PERSON>", "Who Can It Be Now?", "<PERSON> Good Johnny"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "&quot;<PERSON>&quot;, &quot;Turn Back Time&quot; and &quot;Barbie Girl&quot; were UK number ones for which Eurodance group?", "correct_answer": "Aqua", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> 65", "Sash!"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> 65", "Aqua", "Sash!"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Who is the lead singer of Arctic Monkeys?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>&#039;<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>&#039;<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was Daft Punk&#039;s first studio album?", "correct_answer": "Homework", "incorrect_answers": ["Discovery", "Random Access Memories", "Human After All"], "all_answers": ["Discovery", "Random Access Memories", "Human After All", "Homework"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which rap group released the album &quot;Straight Outta Compton&quot;?", "correct_answer": "N.W.A", "incorrect_answers": ["Wu-Tang Clan", "Run-D.M.C.", "Beastie Boys"], "all_answers": ["Wu-Tang Clan", "Run-D.M.C.", "Beastie Boys", "N.W.A"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Which famous rapper is featured in <PERSON> &<PERSON>l; (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>)&#039;s 2015 single called &quot;Febreze&quot;?", "correct_answer": "2 Chainz", "incorrect_answers": ["<PERSON><PERSON>", "Future", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "Future", "2 Chainz", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What is the best selling album of all time from 1976 to 2018?", "correct_answer": "Thriller", "incorrect_answers": ["Back in Black", "Abbey Road", "The Dark Side of the Moon"], "all_answers": ["Back in Black", "Abbey Road", "The Dark Side of the Moon", "Thriller"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "<PERSON>, the lead vocalist of The Police, primarily plays what instrument?", "correct_answer": "Bass Guitar", "incorrect_answers": ["Drums", "Guitar", "Keyboards"], "all_answers": ["Drums", "Bass Guitar", "Guitar", "Keyboards"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What was the subject of the 2014 song &quot;CoCo&quot; by American rapper <PERSON><PERSON> <PERSON><PERSON>?", "correct_answer": "Cocaine", "incorrect_answers": ["<PERSON>&#039;<PERSON>", "Cobalt(II) carbonate", "Coconut cream pie"], "all_answers": ["<PERSON>&#039;<PERSON>", "Cobalt(II) carbonate", "Coconut cream pie", "Cocaine"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "The &quot;K&quot; in &quot;K-Pop&quot; stands for which word?", "correct_answer": "Korean", "incorrect_answers": ["Kenyan", "Kazakhstan", "Kuwaiti"], "all_answers": ["Kenyan", "Kazakhstan", "Kuwaiti", "Korean"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "What is the stage name of English female rapper <PERSON><PERSON><PERSON>, who is known for the song &quot;Paper Planes&quot;?", "correct_answer": "M.I.A.", "incorrect_answers": ["K.I.A.", "C.I.A.", "A.I.A."], "all_answers": ["M.I.A.", "K.I.A.", "C.I.A.", "A.I.A."], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "The 2016 song &quot;Starboy&quot; by Canadian singer <PERSON> Weeknd features which prominent electronic artist?", "correct_answer": "Daft Punk", "incorrect_answers": ["deadmau5", "Disclosure", "DJ <PERSON>"], "all_answers": ["deadmau5", "Disclosure", "DJ <PERSON>", "Daft Punk"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Music", "question": "Who had a 1983 hit with the song &#039;Africa&#039;?", "correct_answer": "Toto", "incorrect_answers": ["Foreigner", "<PERSON><PERSON>", "Journey"], "all_answers": ["Toto", "Foreigner", "<PERSON><PERSON>", "Journey"], "correct_answer_index": 0}], "Entertainment: Television": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the show &quot;Dragonball Z&quot;, what is the name of Cell&#039;s most powerful attack?", "correct_answer": "Solar Kamehameha", "incorrect_answers": ["Super Kamehameha", "Cell Ka<PERSON>ha", "<PERSON>"], "all_answers": ["Super Kamehameha", "Cell Ka<PERSON>ha", "Solar Kamehameha", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In Two and a Half Men, what is <PERSON>&#039;s son&#039;s name?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In Star Trek: The Next Generation, what is the name of Data&#039;s cat?", "correct_answer": "Spot", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "Spot", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What was the name of the police officer in the cartoon &quot;Top Cat&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the show &quot;Futurama&quot; what is <PERSON>&#039;s full name?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "Fry Rodr&iacute;guez", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "Fry Rodr&iacute;guez", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the cartoon &#039;SpongeBob SquarePants&#039;, what did the acronym E.V.I.L stand for?", "correct_answer": "Every Villain Is Lemons", "incorrect_answers": ["Every Villain Is Lemonade", "Every Villain Is <PERSON>es", "Each Villain Is Lemonade"], "all_answers": ["Every Villain Is Lemonade", "Every Villain Is <PERSON>es", "Every Villain Is Lemons", "Each Villain Is Lemonade"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In season one of the Netflix political drama &quot;House of Cards&quot;, what government position does <PERSON> hold?", "correct_answer": "House Majority Whip", "incorrect_answers": ["Attorney General", "President", "Chief of Staff"], "all_answers": ["Attorney General", "House Majority Whip", "President", "Chief of Staff"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "When <PERSON><PERSON> died in the &quot;Donkey Kong Country&quot; episode &quot;It&#039;s a Wonderful Life&quot;, who was his guardian angel?", "correct_answer": "<PERSON> the Mean Old Yeti", "incorrect_answers": ["Kiddy Kong", "<PERSON>dy <PERSON>", "<PERSON> <PERSON>"], "all_answers": ["Kiddy Kong", "<PERSON>dy <PERSON>", "<PERSON> the Mean Old Yeti", "<PERSON> <PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Guy&#039;s Grocery Games is hosted by which presenter?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In &quot;Star Trek: Voyager&quot;, which episode did Voyager establish real-time communication with Starfleet Headquarters?", "correct_answer": "Pathfinder", "incorrect_answers": ["Message In A Bottle", "Someone To Watch Over Me", "Counterpoint"], "all_answers": ["Message In A Bottle", "Someone To Watch Over Me", "Pathfinder", "Counterpoint"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which of the following won the first season of American Idol in 2002?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "In the show Stranger Things, what is Eleven&#039;s favorite breakfast food?", "correct_answer": "<PERSON><PERSON> Waf<PERSON>s", "incorrect_answers": ["Toast", "Captain <PERSON><PERSON>", "Bacon and Eggs"], "all_answers": ["Toast", "Captain <PERSON><PERSON>", "<PERSON><PERSON> Waf<PERSON>s", "Bacon and Eggs"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which actor portrays &quot;<PERSON>&quot; in the series &quot;Breaking Bad&quot;?", "correct_answer": " <PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", " <PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which of these Disney shows is classified as an anime?", "correct_answer": "Stitch!", "incorrect_answers": ["<PERSON> in the House", "The Emperor&#039;s New School", "Hannah <PERSON>"], "all_answers": ["<PERSON> in the House", "The Emperor&#039;s New School", "Stitch!", "Hannah <PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What country is <PERSON> in the House set in?", "correct_answer": "The United States of America", "incorrect_answers": ["Canada", "Venezuala", "England"], "all_answers": ["Canada", "The United States of America", "Venezuala", "England"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Who was the first ever actor to play &quot;The Doctor&quot; on &quot;Doctor Who&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "On the show &quot;<PERSON> and <PERSON><PERSON><PERSON>&quot;, in episode &quot;Total Rickall&quot;, who was a parasite?", "correct_answer": "<PERSON><PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "Mr. <PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Mr. <PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "Which character was played by <PERSON> in the sitcom &#039;Saved by the Bell&#039;?", "correct_answer": "Screech", "incorrect_answers": ["<PERSON>", "Mr. <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "Mr. <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Screech"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What Nickelodeon game show featured a house on the set that the contestants would ransack to find hidden objects?", "correct_answer": "Finders Keepers", "incorrect_answers": ["Double Dare", "Nickelodeon Guts", "<PERSON>"], "all_answers": ["Double Dare", "Nickelodeon Guts", "<PERSON>", "Finders Keepers"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Television", "question": "What was the name of the teenage witch played by <PERSON><PERSON> who lives with her witch aunts <PERSON> and <PERSON><PERSON><PERSON>?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}], "Entertainment: Video Games": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who is the leader of Team Valor in Pok&eacute;mon Go?", "correct_answer": "Candela", "incorrect_answers": ["Willow", "Blanche", "Spark"], "all_answers": ["Willow", "Blanche", "Spark", "Candela"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In &quot;Call Of Duty: Zombies&quot;, what is the name of the machine that upgrades weapons?", "correct_answer": "Pack-A-Punch", "incorrect_answers": ["Wunderfizz", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["Wunderfizz", "<PERSON><PERSON><PERSON>", "Pack-A-Punch", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who&#039;s the voice actor for <PERSON><PERSON><PERSON> in the Warcraft game series?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In &quot;Yo! Noid 2,&quot; The Noid can perform what special move?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Pizza Throw", "Dodge Roll", "Spin Dash"], "all_answers": ["Pizza Throw", "<PERSON><PERSON>", "Dodge Roll", "Spin Dash"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In which mall does &quot;Dead Rising&quot; take place?", "correct_answer": "Willamette Parkview Mall", "incorrect_answers": ["Liberty Mall", "Twin Pines Mall", "Central Square Shopping Center"], "all_answers": ["Willamette Parkview Mall", "Liberty Mall", "Twin Pines Mall", "Central Square Shopping Center"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In the game &quot;Fire Emblem: Shadow Dragon&quot;, what is the central protagonist&#039;s name?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In the game Battleblock Theater, what was the name of the voice actor who voiced the narrator?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In the video game franchise &quot;Halo&quot;, what is the UNSC&#039;s main opposing faction called?", "correct_answer": "The Covenant", "incorrect_answers": ["The Reckoning", "The Peoples", "The Slaughterers"], "all_answers": ["The Reckoning", "The Peoples", "The Covenant", "The Slaughterers"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What game mode was not in the original &quot;Wii Sports&quot;?", "correct_answer": "Table Tennis", "incorrect_answers": ["Boxing", "Baseball", "Bowling"], "all_answers": ["Boxing", "Baseball", "Table Tennis", "Bowling"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In &quot;A Hat in Time&quot;, what must <PERSON> collect to finish a level", "correct_answer": "A time piece", "incorrect_answers": ["A heart fragment", "A relic fragment", "A hat"], "all_answers": ["A heart fragment", "A relic fragment", "A hat", "A time piece"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which <PERSON> the Hedgehog game was originally supposed to be packaged with Sonic 3, but was cut in half due to time constraints?", "correct_answer": "Sonic &amp; Knuckles", "incorrect_answers": ["Sonic 2", "Sonic CD", "Sonic 3D Blast"], "all_answers": ["Sonic 2", "Sonic CD", "Sonic 3D Blast", "Sonic &amp; Knuckles"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "When was the game &#039;Portal 2&#039; released?", "correct_answer": "2011", "incorrect_answers": ["2014", "2009", "2007"], "all_answers": ["2014", "2009", "2007", "2011"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What year was Super Mario Bros. released?", "correct_answer": "1985", "incorrect_answers": ["1983", "1987", "1986"], "all_answers": ["1983", "1985", "1987", "1986"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What war is Call Of Duty: Black Ops based on?", "correct_answer": "Cold War", "incorrect_answers": ["WW3", "Vietnam", "WW1"], "all_answers": ["WW3", "Vietnam", "WW1", "Cold War"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which of these is NOT a team available in the game Pok&eacute;mon Go?", "correct_answer": "Team Rocket", "incorrect_answers": ["Team Instinct", "Team Valor", "Team Mystic"], "all_answers": ["Team Rocket", "Team Instinct", "Team Valor", "Team Mystic"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Which Grand Theft Auto (GTA) games have the same setting?", "correct_answer": "GTA V and GTA San Andreas", "incorrect_answers": ["GTA V and GTA Vice City", "GTA IV and GTA San Andreas", "GTA IV and GTA Vice City"], "all_answers": ["GTA V and GTA Vice City", "GTA V and GTA San Andreas", "GTA IV and GTA San Andreas", "GTA IV and GTA Vice City"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "In the Video Game, Half-life, what type of US military force starts clearing out the Black Mesa Research Facility?", "correct_answer": "The HECU", "incorrect_answers": ["Navy Seals", "The Combine", "The Marines"], "all_answers": ["Navy Seals", "The HECU", "The Combine", "The Marines"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who is the main protagonist in the &#039;<PERSON>chet and Clank&#039; game series?", "correct_answer": "Ratchet", "incorrect_answers": ["Clank", "Captin Qwark", "<PERSON><PERSON>"], "all_answers": ["Ratchet", "Clank", "Captin Qwark", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "What was the first game in the &quot;Battlefield&quot; series?", "correct_answer": "Battlefield 1942", "incorrect_answers": ["Battlefield Vietnam", "Battlefield 2", "Battlefield 1"], "all_answers": ["Battlefield 1942", "Battlefield Vietnam", "Battlefield 2", "Battlefield 1"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Video Games", "question": "Who in this list is one of the co-founders of Valve?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}], "Entertainment: Board Games": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "Which of these games includes the phrase &quot;Do not pass Go, do not collect $200&quot;?", "correct_answer": "Monopoly", "incorrect_answers": ["Pay Day", "<PERSON><PERSON><PERSON>", "Coppit"], "all_answers": ["Monopoly", "Pay Day", "<PERSON><PERSON><PERSON>", "Coppit"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "How many dice are used in the game of Yahtzee?", "correct_answer": "Five", "incorrect_answers": ["Four", "Six", "Eight"], "all_answers": ["Four", "Six", "Eight", "Five"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "When was the board game <PERSON>wister, released to the public?", "correct_answer": "April 1966", "incorrect_answers": ["September 1965", "January 1969", "February 1966"], "all_answers": ["September 1965", "January 1969", "April 1966", "February 1966"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "How many pieces are there on the board at the start of a game of chess?", "correct_answer": "32", "incorrect_answers": ["16", "20", "36"], "all_answers": ["32", "16", "20", "36"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "In which year was the pen and paper RPG &quot;Deadlands&quot; released?", "correct_answer": "1996", "incorrect_answers": ["2003", "1999", "1993"], "all_answers": ["1996", "2003", "1999", "1993"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "On a standard Monopoly board, which square is diagonally opposite &#039;Go&#039;? ", "correct_answer": "Free Parking", "incorrect_answers": ["Go to Jail", "Jail", "The Electric Company"], "all_answers": ["Go to Jail", "Jail", "Free Parking", "The Electric Company"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "In a standard game of Monopoly, what colour are the two cheapest properties?", "correct_answer": "<PERSON>", "incorrect_answers": ["Green", "Yellow", "Blue"], "all_answers": ["<PERSON>", "Green", "Yellow", "Blue"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "In Dungeons and Dragons (5th edition), what stat do you normally add onto your initiative die roll?", "correct_answer": "Dexterity", "incorrect_answers": ["Speed", "Strength", "Wisdom"], "all_answers": ["Speed", "Strength", "Dexterity", "Wisdom"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "In chess, which is a characteristic of the King&#039;s Gambit?", "correct_answer": "Sacrificing a pawn", "incorrect_answers": ["Sacrificing the king for a queen", "Castling kingside", "Moving the king on the 2nd move"], "all_answers": ["Sacrificing a pawn", "Sacrificing the king for a queen", "Castling kingside", "Moving the king on the 2nd move"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Board Games", "question": "How many spaces are there on a standard Monopoly board?", "correct_answer": "40", "incorrect_answers": ["28", "55", "36"], "all_answers": ["40", "28", "55", "36"], "correct_answer_index": 0}], "Science & Nature": [{"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the first element on the periodic table?", "correct_answer": "Hydrogen", "incorrect_answers": ["Helium", "Oxygen", "Lithium"], "all_answers": ["Helium", "Hydrogen", "Oxygen", "Lithium"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Dry ice is the solid form of what substance?", "correct_answer": "Carbon dioxide", "incorrect_answers": ["Nitrogen", "Ammonia", "Oxygen"], "all_answers": ["Nitrogen", "Ammonia", "Carbon dioxide", "Oxygen"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What does DNA stand for?", "correct_answer": "Deoxyribonucleic Acid", "incorrect_answers": ["Deoxyribogenetic Acid", "Deoxyribogenetic Atoms", "Detoxic Acid"], "all_answers": ["Deoxyribogenetic Acid", "Deoxyribogenetic Atoms", "Detoxic Acid", "Deoxyribonucleic Acid"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "How many planets are in our Solar System?", "correct_answer": "Eight", "incorrect_answers": ["Nine", "Seven", "Ten"], "all_answers": ["Nine", "Eight", "Seven", "Ten"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which of the following bones is not in the leg?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "Tibia", "Fibula "], "all_answers": ["<PERSON><PERSON>", "Tibia", "Fibula ", "<PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which type of rock is created by intense heat AND pressure?", "correct_answer": "Metamorphic", "incorrect_answers": ["Sedimentary", "Igneous", "Diamond"], "all_answers": ["Sedimentary", "Igneous", "Metamorphic", "Diamond"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which noble gas has the lowest atomic number?", "correct_answer": "Helium", "incorrect_answers": ["Neon", "Argon", "Krypton"], "all_answers": ["Neon", "Argon", "Krypton", "Helium"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "The asteroid belt is located between which two planets?", "correct_answer": "Mars and Jupiter", "incorrect_answers": ["Jupiter and Saturn", "Mercury and Venus", "Earth and Mars"], "all_answers": ["Mars and Jupiter", "Jupiter and Saturn", "Mercury and Venus", "Earth and Mars"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Alzheimer&#039;s disease primarily affects which part of the human body?", "correct_answer": "Brain", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Skin", "Heart"], "all_answers": ["<PERSON><PERSON><PERSON>", "Brain", "Skin", "Heart"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the standard atomic weight of a Plutonium nucleus?", "correct_answer": "244", "incorrect_answers": ["94", "481", "128"], "all_answers": ["94", "481", "244", "128"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the hottest planet in the solar system", "correct_answer": "Venus", "incorrect_answers": ["Jupiter", "Mercury ", "Mars"], "all_answers": ["Jupiter", "Venus", "Mercury ", "Mars"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What does LASER stand for?", "correct_answer": "Light amplification by stimulated emission of radiation", "incorrect_answers": ["Lite analysing by stereo ecorazer", "Light amplifier by standby energy of radio", "Life antimatter by standing entry of range"], "all_answers": ["Lite analysing by stereo ecorazer", "Light amplification by stimulated emission of radiation", "Light amplifier by standby energy of radio", "Life antimatter by standing entry of range"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "How many planets make up our Solar System?", "correct_answer": "8", "incorrect_answers": ["7", "9", "6"], "all_answers": ["7", "8", "9", "6"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What is the &quot;powerhouse&quot; of the Eukaryotic animal cell?", "correct_answer": "Mitochondria", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "Chloroplast", "Endoplasmic Reticulum"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "Mitochondria", "Chloroplast", "Endoplasmic Reticulum"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "The medical term for the belly button is which of the following?", "correct_answer": "Umbilicus", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>xi<PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>xi<PERSON>", "Umbilicus"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which element has the chemical symbol &#039;Fe&#039;?", "correct_answer": "Iron", "incorrect_answers": ["Gold", "Silver", "Tin"], "all_answers": ["Gold", "Iron", "Silver", "Tin"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What name is given to all baby marsupials?", "correct_answer": "<PERSON>", "incorrect_answers": ["Calf", "<PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["Calf", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "What does the letter &#039;S&#039; stand for in &#039;NASA&#039;?", "correct_answer": "Space", "incorrect_answers": ["Science", "Society", "Star"], "all_answers": ["Science", "Society", "Space", "Star"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "Which Apollo mission was the first one to land on the Moon?", "correct_answer": "Apollo 11", "incorrect_answers": ["Apollo 10", "Apollo 9", "Apollo 13"], "all_answers": ["Apollo 10", "Apollo 9", "Apollo 13", "Apollo 11"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science &amp; Nature", "question": "71% of the Earth&#039;s surface is made up of", "correct_answer": "Water", "incorrect_answers": ["Deserts", "Continents", "Forests"], "all_answers": ["Water", "Deserts", "Continents", "Forests"], "correct_answer_index": 0}], "Science: Computers": [{"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does LTS stand for in the software market?", "correct_answer": "Long Term Support", "incorrect_answers": ["Long Taco Service", "Ludicrous Transfer Speed", "Ludicrous Turbo Speed"], "all_answers": ["Long Taco Service", "Ludicrous Transfer Speed", "Ludicrous Turbo Speed", "Long Term Support"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In any programming language, what is the most common way to iterate through an array?", "correct_answer": "&#039;For&#039; loops", "incorrect_answers": ["&#039;If&#039; Statements", "&#039;Do-while&#039; loops", "&#039;While&#039; loops"], "all_answers": ["&#039;If&#039; Statements", "&#039;Do-while&#039; loops", "&#039;While&#039; loops", "&#039;For&#039; loops"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does CPU stand for?", "correct_answer": "Central Processing Unit", "incorrect_answers": ["Central Process Unit", "Computer Personal Unit", "Central Processor Unit"], "all_answers": ["Central Process Unit", "Computer Personal Unit", "Central Processor Unit", "Central Processing Unit"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "Which company was established on April 1st, 1976 by <PERSON>, <PERSON> and <PERSON>?", "correct_answer": "Apple", "incorrect_answers": ["Microsoft", "Atari", "Commodore"], "all_answers": ["Microsoft", "Apple", "Atari", "Commodore"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "On a standard American QWERTY keyboard, what symbol will you enter if you hold the shift key and press 1?", "correct_answer": "Exclamation Mark", "incorrect_answers": ["Dollar Sign", "Percent Sign", "Asterisk"], "all_answers": ["Dollar Sign", "Percent Sign", "Asterisk", "Exclamation Mark"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What is the most preferred image format used for logos in the Wikimedia database?", "correct_answer": ".svg", "incorrect_answers": [".png", ".jpeg", ".gif"], "all_answers": [".svg", ".png", ".jpeg", ".gif"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What is the name of Layer 7 of the OSI model?", "correct_answer": "Application", "incorrect_answers": ["Session", "Network", "Present"], "all_answers": ["Session", "Application", "Network", "Present"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The numbering system with a radix of 16 is more commonly referred to as ", "correct_answer": "Hexidecimal", "incorrect_answers": ["Binary", "Duodecimal", "Octal"], "all_answers": ["Binary", "Duodecimal", "Hexidecimal", "Octal"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does the &quot;MP&quot; stand for in MP3?", "correct_answer": "Moving Picture", "incorrect_answers": ["Music Player", "Multi Pass", "Micro Point"], "all_answers": ["Music Player", "Moving Picture", "Multi Pass", "Micro Point"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What does the computer software acronym JVM stand for?", "correct_answer": "Java Virtual Machine", "incorrect_answers": ["Java Vendor Machine", "Java Visual Machine", "Just Virtual Machine"], "all_answers": ["Java Vendor Machine", "Java Visual Machine", "Java Virtual Machine", "Just Virtual Machine"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "In computing, what does MIDI stand for?", "correct_answer": "Musical Instrument Digital Interface", "incorrect_answers": ["Musical Interface of Digital Instruments", "Modular Interface of Digital Instruments", "Musical Instrument Data Interface"], "all_answers": ["Musical Interface of Digital Instruments", "Modular Interface of Digital Instruments", "Musical Instrument Data Interface", "Musical Instrument Digital Interface"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The programming language &#039;Swift&#039; was created to replace what other programming language?", "correct_answer": "Objective-C", "incorrect_answers": ["C#", "<PERSON>", "C++"], "all_answers": ["C#", "<PERSON>", "C++", "Objective-C"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "How many kilobytes in one gigabyte (in decimal)?", "correct_answer": "1000000", "incorrect_answers": ["1024", "1000", "1048576"], "all_answers": ["1024", "1000", "1000000", "1048576"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "HTML is what type of language?", "correct_answer": "Markup Language", "incorrect_answers": ["Macro Language", "Programming Language", "Scripting Language"], "all_answers": ["Markup Language", "Macro Language", "Programming Language", "Scripting Language"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "What programming language was GitHub written in?", "correct_answer": "<PERSON>", "incorrect_answers": ["JavaScript", "Python", "<PERSON><PERSON>"], "all_answers": ["JavaScript", "<PERSON>", "Python", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The C programming language was created by this American computer scientist. ", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "al-Khw<PERSON>rizmī", "<PERSON>"], "all_answers": ["<PERSON>", "al-Khw<PERSON>rizmī", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "The series of the Intel HD graphics generation succeeding that of the 5000 and 6000 series (Broadwell) is called:", "correct_answer": "HD Graphics 500", "incorrect_answers": ["HD Graphics 700 ", "HD Graphics 600", "HD Graphics 7000"], "all_answers": ["HD Graphics 500", "HD Graphics 700 ", "HD Graphics 600", "HD Graphics 7000"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "Which programming language shares its name with an island in Indonesia?", "correct_answer": "Java", "incorrect_answers": ["Python", "C", "Jakarta"], "all_answers": ["Python", "C", "Java", "Jakarta"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "If you were to code software in this language you&#039;d only be able to type 0&#039;s and 1&#039;s.", "correct_answer": "Binary", "incorrect_answers": ["JavaScript", "C++", "Python"], "all_answers": ["Binary", "JavaScript", "C++", "Python"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Computers", "question": "How many values can a single byte represent?", "correct_answer": "256", "incorrect_answers": ["8", "1", "1024"], "all_answers": ["256", "8", "1", "1024"], "correct_answer_index": 0}], "Science: Mathematics": [{"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What type of angle is greater than 90&deg;?", "correct_answer": "Obtuse", "incorrect_answers": ["Acute", "Right", "Straight"], "all_answers": ["Obtuse", "Acute", "Right", "Straight"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "The metric prefix &quot;atto-&quot; makes a measurement how much smaller than the base unit?", "correct_answer": "One Quintillionth", "incorrect_answers": ["One Billionth", "One Quadrillionth", "One Septillionth"], "all_answers": ["One Quintillionth", "One Billionth", "One Quadrillionth", "One Septillionth"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What is the equation for the area of a sphere?", "correct_answer": "(4/3)&pi;r^3", "incorrect_answers": ["4&pi;r^2", "(1/3)&pi;hr^2", "&pi;r^4"], "all_answers": ["(4/3)&pi;r^3", "4&pi;r^2", "(1/3)&pi;hr^2", "&pi;r^4"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What&#039;s the square root of 49?", "correct_answer": "7", "incorrect_answers": ["4", "12", "9"], "all_answers": ["7", "4", "12", "9"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "How many sides does a heptagon have?", "correct_answer": "7", "incorrect_answers": ["8", "6", "5"], "all_answers": ["8", "6", "5", "7"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What is the correct order of operations for solving equations?", "correct_answer": "Parentheses, Exponents, Multiplication, Division, Addition, Subtraction", "incorrect_answers": ["Addition, Multiplication, Division, Subtraction, Addition, Parentheses", "Parentheses, Exponents, Addition, Substraction, Multiplication, Division", "The order in which the operations are written."], "all_answers": ["Addition, Multiplication, Division, Subtraction, Addition, Parentheses", "Parentheses, Exponents, Addition, Substraction, Multiplication, Division", "Parentheses, Exponents, Multiplication, Division, Addition, Subtraction", "The order in which the operations are written."], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "How many sides does a trapezium have?", "correct_answer": "4", "incorrect_answers": ["3", "5", "6"], "all_answers": ["3", "4", "5", "6"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What is the symbol for Displacement?", "correct_answer": "&Delta;r", "incorrect_answers": ["dr", "Dp", "r"], "all_answers": ["dr", "Dp", "&Delta;r", "r"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "What prime number comes next after 19?", "correct_answer": "23", "incorrect_answers": ["25", "21", "27"], "all_answers": ["23", "25", "21", "27"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Mathematics", "question": "In Roman Numerals, what does XL equate to?", "correct_answer": "40", "incorrect_answers": ["60", "15", "90"], "all_answers": ["60", "15", "90", "40"], "correct_answer_index": 3}], "Mythology": [{"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "Which figure from Greek mythology traveled to the underworld to return his wife <PERSON><PERSON><PERSON> to the land of the living?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Hercules", "Per<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "Hercules", "Per<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "The ancient Roman god of war was commonly known as which of the following?", "correct_answer": "Mars", "incorrect_answers": ["Jupiter", "Juno", "Ares"], "all_answers": ["Jupiter", "Juno", "Ares", "Mars"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "In most traditions, who was the wife of <PERSON>?", "correct_answer": "<PERSON>a", "incorrect_answers": ["Aphrodite", "Athena", "<PERSON><PERSON><PERSON>"], "all_answers": ["Aphrodite", "<PERSON>a", "Athena", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "In Greek mythology, who is the god of wine?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "Demeter", "Apollo"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Demeter", "Apollo"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "The Nike apparel and footwear brand takes it&#039;s name from the Greek goddess of what?", "correct_answer": "Victory", "incorrect_answers": ["Courage", "Strength", "Honor"], "all_answers": ["Courage", "Strength", "Honor", "Victory"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "The greek god <PERSON><PERSON><PERSON> was the god of what?", "correct_answer": "The Sea", "incorrect_answers": ["War", "Sun", "Fire"], "all_answers": ["War", "Sun", "Fire", "The Sea"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "Which of the following is not true about the life of <PERSON><PERSON><PERSON>?", "correct_answer": "Sailed with the Argonauts to find the golden fleece", "incorrect_answers": ["<PERSON> turned him into a woman, and then years later back into a man", "<PERSON><PERSON> blinded him after he agreed with <PERSON> in an argument", "Revealed to <PERSON><PERSON><PERSON><PERSON> that <PERSON><PERSON><PERSON><PERSON> had married his own mother"], "all_answers": ["Sailed with the Argonauts to find the golden fleece", "<PERSON> turned him into a woman, and then years later back into a man", "<PERSON><PERSON> blinded him after he agreed with <PERSON> in an argument", "Revealed to <PERSON><PERSON><PERSON><PERSON> that <PERSON><PERSON><PERSON><PERSON> had married his own mother"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "Who in Greek mythology, who led the Argonauts in search of the Golden Fleece?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "Which Greek &amp; Roman god was known as the god of music, truth and prophecy, healing, the sun and light, plague, poetry, and more?", "correct_answer": "Apollo", "incorrect_answers": ["Aphrodite", "Artemis", "Athena"], "all_answers": ["Aphrodite", "Apollo", "Artemis", "Athena"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Mythology", "question": "Who was the only god from Greece who did not get a name change in Rome?", "correct_answer": "Apollo", "incorrect_answers": ["Demeter", "Zeus", "Athena"], "all_answers": ["Demeter", "Zeus", "Athena", "Apollo"], "correct_answer_index": 3}], "Sports": [{"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which driver has been the Formula 1 world champion for a record 7 times?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "How many players are there in an association football/soccer team?", "correct_answer": "11", "incorrect_answers": ["10", "9", "8"], "all_answers": ["10", "9", "8", "11"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What is the most common type of pitch thrown by pitchers in baseball?", "correct_answer": "Fastball", "incorrect_answers": ["Slowball", "Screwball", "Palmball"], "all_answers": ["Slowball", "Fastball", "Screwball", "Palmball"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which year did <PERSON><PERSON> won his first ever Formula One World Drivers&#039; Championship?", "correct_answer": "2009", "incorrect_answers": ["2010", "2007", "2006"], "all_answers": ["2010", "2007", "2006", "2009"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which African American is in part responsible for integrating  Major League baseball?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What team won the 2016 MLS Cup?", "correct_answer": "Seattle Sounders", "incorrect_answers": ["Colorado Rapids", "Toronto FC", "Montreal Impact"], "all_answers": ["Seattle Sounders", "Colorado Rapids", "Toronto FC", "Montreal Impact"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What year did the New Orleans Saints win the Super Bowl?", "correct_answer": "2010", "incorrect_answers": ["2008", "2009", "2011"], "all_answers": ["2008", "2010", "2009", "2011"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "How many points did <PERSON><PERSON><PERSON> score in his first NBA game?", "correct_answer": "25", "incorrect_answers": ["19", "69", "41"], "all_answers": ["19", "25", "69", "41"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What is the name of Manchester United&#039;s home stadium?", "correct_answer": "Old Trafford", "incorrect_answers": ["Anfield", "City of Manchester Stadium", "St James Park"], "all_answers": ["Anfield", "City of Manchester Stadium", "Old Trafford", "St James Park"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "What was the final score of the Germany vs. Brazil 2014 FIFA World Cup match?", "correct_answer": "7 - 1", "incorrect_answers": ["0 - 1", "3 - 4", "16 - 0"], "all_answers": ["7 - 1", "0 - 1", "3 - 4", "16 - 0"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which of the following sports is not part of the triathlon?", "correct_answer": "Horse-Riding", "incorrect_answers": ["Cycling", "Swimming", "Running"], "all_answers": ["Cycling", "Horse-Riding", "Swimming", "Running"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which wrestler won the 2019 Men&rsquo;s Royal Rumble?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "In baseball, how many fouls are an out?", "correct_answer": "0", "incorrect_answers": ["5", "3", "2"], "all_answers": ["5", "3", "0", "2"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which NFL team plays for New England?", "correct_answer": "Patriots", "incorrect_answers": ["Chiefs", "Dolphins", "49ers"], "all_answers": ["Chiefs", "Dolphins", "Patriots", "49ers"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which two teams played in Super Bowl XLII?", "correct_answer": "The New York Giants &amp; The New England Patriots", "incorrect_answers": ["The Green Bay Packers &amp; The Pittsburgh Steelers", "The Philadelphia Eagles &amp; The New England Patriots", "The Seattle Seahawks &amp; The Denver Broncos"], "all_answers": ["The Green Bay Packers &amp; The Pittsburgh Steelers", "The Philadelphia Eagles &amp; The New England Patriots", "The New York Giants &amp; The New England Patriots", "The Seattle Seahawks &amp; The Denver Broncos"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which team won the 2015-16 English Premier League?", "correct_answer": "Leicester City", "incorrect_answers": ["Liverpool", "<PERSON><PERSON><PERSON>", "Manchester United"], "all_answers": ["Liverpool", "<PERSON><PERSON><PERSON>", "Leicester City", "Manchester United"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "In golf, what name is given to a hole score of two under par?", "correct_answer": "Eagle", "incorrect_answers": ["<PERSON><PERSON>", "Bogey", "Albatross"], "all_answers": ["Eagle", "<PERSON><PERSON>", "Bogey", "Albatross"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Who did <PERSON> win the Champions League with?", "correct_answer": "Liverpool", "incorrect_answers": ["Real Madrid", "Chelsea", "Man City"], "all_answers": ["Real Madrid", "Liverpool", "Chelsea", "Man City"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "Which team won 2014 FIFA World Cup in Brazil?", "correct_answer": "Germany", "incorrect_answers": ["Argentina", "Brazil", "Netherlands"], "all_answers": ["Argentina", "Brazil", "Germany", "Netherlands"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Sports", "question": "When was the first official international game played?", "correct_answer": "1872", "incorrect_answers": ["1880", "1863", "1865"], "all_answers": ["1880", "1863", "1865", "1872"], "correct_answer_index": 3}], "Geography": [{"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the name of the peninsula containing Spain and Portugal?", "correct_answer": "Iberian Peninsula", "incorrect_answers": ["European Peninsula", "Peloponnesian Peninsula", "Scandinavian Peninsula"], "all_answers": ["European Peninsula", "Peloponnesian Peninsula", "Iberian Peninsula", "Scandinavian Peninsula"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which of the following Japanese islands is the biggest?", "correct_answer": "Honshu", "incorrect_answers": ["Hokkaido", "Shikoku", "Kyushu"], "all_answers": ["Hokkaido", "Shikoku", "Kyushu", "Honshu"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the official language of Costa Rica?", "correct_answer": "Spanish", "incorrect_answers": ["English", "Portuguese", "Creole"], "all_answers": ["English", "Portuguese", "Creole", "Spanish"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of Scotland?", "correct_answer": "Edinburgh", "incorrect_answers": ["Glasgow", "Dundee", "London"], "all_answers": ["Glasgow", "Edinburgh", "Dundee", "London"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the largest country in the world ?", "correct_answer": "Russian Federation", "incorrect_answers": ["China", "Canada", "Brazil"], "all_answers": ["China", "Canada", "Brazil", "Russian Federation"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which country is the home of the largest Japanese population outside of Japan?", "correct_answer": "Brazil", "incorrect_answers": ["China", "Russia", "The United States"], "all_answers": ["China", "Brazil", "Russia", "The United States"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of the American state of Arizona?", "correct_answer": "Phoenix", "incorrect_answers": ["<PERSON>", "Tallahassee", "Raleigh"], "all_answers": ["<PERSON>", "Tallahassee", "Phoenix", "Raleigh"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "How many countries does Mexico border?", "correct_answer": "3", "incorrect_answers": ["2", "4", "1"], "all_answers": ["2", "4", "3", "1"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What country is the second largest in the world by area?", "correct_answer": "Canada", "incorrect_answers": ["Russia", "China", "United States of America"], "all_answers": ["Russia", "Canada", "China", "United States of America"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which small country is located between the borders of France and Spain?", "correct_answer": "Andorra", "incorrect_answers": ["San Marino", "Vatican City", "Lichtenstein"], "all_answers": ["San Marino", "Vatican City", "Andorra", "Lichtenstein"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What country has a horizontal bicolor red and white flag?", "correct_answer": "Monaco", "incorrect_answers": ["Bahrain", "Malta", "<PERSON><PERSON>stein"], "all_answers": ["Monaco", "Bahrain", "Malta", "<PERSON><PERSON>stein"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "How many countries does the United States share a land border with?", "correct_answer": "2", "incorrect_answers": ["1", "3", "4"], "all_answers": ["1", "3", "2", "4"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the nickname for the US state Delaware?", "correct_answer": "The First State", "incorrect_answers": ["The Fiftieth State", "The Second State", "The Sixteenth State"], "all_answers": ["The First State", "The Fiftieth State", "The Second State", "The Sixteenth State"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which of the following European languages is classified as a &quot;language isolate?&quot;", "correct_answer": "Basque", "incorrect_answers": ["Galician", "Maltese", "Hungarian"], "all_answers": ["Basque", "Galician", "Maltese", "Hungarian"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "Which city is the capital of the United States of America?", "correct_answer": "Washington D.C", "incorrect_answers": ["Seattle", "Albany", "Los Angeles"], "all_answers": ["Washington D.C", "Seattle", "Albany", "Los Angeles"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the Capital of the United States?", "correct_answer": "Washington, D.C.", "incorrect_answers": ["Los Angelas, CA", "New York City, NY", "Houston, TX"], "all_answers": ["Los Angelas, CA", "Washington, D.C.", "New York City, NY", "Houston, TX"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of South Korea?", "correct_answer": "Seoul", "incorrect_answers": ["Pyongyang", "Taegu", "Kitakyushu"], "all_answers": ["Pyongyang", "Taegu", "Seoul", "Kitakyushu"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is Laos?", "correct_answer": "Country", "incorrect_answers": ["Region", "River", "City"], "all_answers": ["Region", "River", "City", "Country"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "The Space Needle is located in which city?", "correct_answer": "Seattle", "incorrect_answers": ["Los Angles", "Toronto", "Vancouver"], "all_answers": ["Seattle", "Los Angles", "Toronto", "Vancouver"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Geography", "question": "What is the capital of India?", "correct_answer": "New Delhi", "incorrect_answers": ["Beijing", "Montreal", "<PERSON>ithi"], "all_answers": ["Beijing", "New Delhi", "Montreal", "<PERSON>ithi"], "correct_answer_index": 1}], "History": [{"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which one of these countries was NOT in the Central Powers during WWI?", "correct_answer": "Spain", "incorrect_answers": ["Austria-Hungary", "Turkey", "Germany"], "all_answers": ["Austria-Hungary", "Spain", "Turkey", "Germany"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who was the Prime Minister of Japan when Japan declared war on the US?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "How many manned moon landings have there been?", "correct_answer": "6", "incorrect_answers": ["1", "3", "7"], "all_answers": ["1", "3", "6", "7"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "What was the name commonly given to the ancient trade routes that connected the East and West of Eurasia?", "correct_answer": "Silk Road", "incorrect_answers": ["Spice Road", "Clay Road", "Salt Road"], "all_answers": ["Spice Road", "Clay Road", "Silk Road", "Salt Road"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which one of these was not a beach landing site in the Invasion of Normandy?", "correct_answer": "Silver", "incorrect_answers": ["Gold", "Juno", "Sword"], "all_answers": ["Gold", "Silver", "Juno", "Sword"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which modern country is known as &quot;The Graveyard of Empires&quot;?", "correct_answer": "Afghanistan", "incorrect_answers": ["China", "Iraq", "Russia"], "all_answers": ["Afghanistan", "China", "Iraq", "Russia"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which famous military commander marched an army, which included war elephants, over the Alps during the Second Punic War?", "correct_answer": "Hannibal", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON> the Great", "<PERSON><PERSON><PERSON>"], "all_answers": ["Hannibal", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> the Great", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which country was <PERSON> born in?", "correct_answer": "Georgia", "incorrect_answers": ["Russia", "Germany", "Poland"], "all_answers": ["Russia", "Germany", "Poland", "Georgia"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who was among those killed in the 2010 Smolensk, Russia plane crash tragedy?", "correct_answer": "The Polish President", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "all_answers": ["The Polish President", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who rode on horseback to warn the Minutemen that the British were coming during the U.S. Revolutionary War?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which one of these tanks was designed and operated by the United Kingdom?", "correct_answer": "Tog II", "incorrect_answers": ["M4 Sherman", "Tiger H1", "T-34"], "all_answers": ["M4 Sherman", "Tiger H1", "Tog II", "T-34"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "In what year did the Wall Street Crash take place?", "correct_answer": "1929", "incorrect_answers": ["1932", "1930", "1925"], "all_answers": ["1932", "1929", "1930", "1925"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "To what political party did <PERSON> belong when elected POTUS?", "correct_answer": "Republican", "incorrect_answers": ["Democrat", "Independent", "Whig"], "all_answers": ["Democrat", "Independent", "Whig", "Republican"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Which modern day country is the region that was known as Phrygia in ancient times?", "correct_answer": "Turkey", "incorrect_answers": ["Syria", "Greece", "Egypt"], "all_answers": ["Syria", "Greece", "Egypt", "Turkey"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "The idea of Socialism was articulated and advanced by whom?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "When did the Battle of the Somme begin?", "correct_answer": "July 1st, 1916", "incorrect_answers": ["August 1st, 1916", "July 2nd, 1916", "June 30th, 1916"], "all_answers": ["August 1st, 1916", "July 2nd, 1916", "July 1st, 1916", "June 30th, 1916"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "Who discovered Penicillin?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "During WWII, in 1945, the United States dropped atomic bombs on the two Japanese cities of Hiroshima and what other city?", "correct_answer": "Nagasaki", "incorrect_answers": ["Kawasaki", "Tokyo", "Kagoshima"], "all_answers": ["Kawasaki", "Nagasaki", "Tokyo", "Kagoshima"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "What does the United States of America celebrate during the 4th of July?", "correct_answer": "The signing of the Declaration of Independence", "incorrect_answers": ["The anniversary of the Battle of Gettysburg", "The crossing of the Delaware River", "The ratification of the Constitution"], "all_answers": ["The anniversary of the Battle of Gettysburg", "The signing of the Declaration of Independence", "The crossing of the Delaware River", "The ratification of the Constitution"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "History", "question": "What is the historical name of Sri Lanka?", "correct_answer": "Ceylon", "incorrect_answers": ["Myanmar", "Colombo", "Badulla"], "all_answers": ["Ceylon", "Myanmar", "Colombo", "Badulla"], "correct_answer_index": 0}], "Art": [{"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted &quot;<PERSON><PERSON> Reflecting Elephants&quot;, &quot;Sleep&quot;, and &quot;The Persistence of Memory&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Which <PERSON> painting depicts the view from his asylum in Saint-R&eacute;my-de-Provence in southern France?", "correct_answer": "The Starry Night", "incorrect_answers": ["Wheatfields with Crows", "The Sower with Setting Sun", "The Church at Auvers"], "all_answers": ["Wheatfields with Crows", "The Sower with Setting Sun", "The Starry Night", "The Church at Auvers"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted &quot;The Starry Night&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted The Starry Night?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted the Mona Lisa?", "correct_answer": "<PERSON> ", "incorrect_answers": ["<PERSON>", " <PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", " <PERSON>", "<PERSON><PERSON>", "<PERSON> "], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "What is the name of the Japanese art of folding paper into decorative shapes and figures?", "correct_answer": "Origami", "incorrect_answers": ["Sumi-e", "Ukiyo-e", "Hai<PERSON>"], "all_answers": ["Sumi-e", "Origami", "Ukiyo-e", "Hai<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted the biblical fresco The Creation of Adam?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "Caravaggio", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "Caravaggio", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Which artist painted the late 15th century mural &#039;The Last Supper&#039;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Who painted the Sistine Chapel?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Art", "question": "Which painting was not made by <PERSON>?", "correct_answer": "The Ninth Wave", "incorrect_answers": ["Caf&eacute; Terrace at Night", "Bedroom In Arles", "Starry Night"], "all_answers": ["Caf&eacute; Terrace at Night", "Bedroom In Arles", "The Ninth Wave", "Starry Night"], "correct_answer_index": 2}], "Celebrities": [{"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "What was the cause of <PERSON> suicide?", "correct_answer": "Drug Overdose", "incorrect_answers": ["Knife Attack", "House Fire", "Gunshot"], "all_answers": ["Knife Attack", "House Fire", "Drug Overdose", "Gunshot"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "<PERSON> is played by which comedian?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "Named after a character he played in a 1969 film, what is the name of the ski resort in Utah that <PERSON> bought in 1968?", "correct_answer": "Sundance", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "Sundance"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "What was <PERSON>&#039;s last film role before his death?", "correct_answer": "American Gun", "incorrect_answers": ["Monsters Inc", "Texas Rangers", "Snow Dogs"], "all_answers": ["Monsters Inc", "American Gun", "Texas Rangers", "Snow Dogs"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "<PERSON> is better known as", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "2 Chainz"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "2 Chainz"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "By which name is <PERSON> better known as?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "Which celebrity announced his presidency in 2015?", "correct_answer": "Kanye West", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Kanye West"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "What does film maker <PERSON> typically focus his films on?", "correct_answer": "Abandoned Buildings and Dead Malls", "incorrect_answers": ["Historic Landmarks", "Action Films", "Documentaries "], "all_answers": ["Abandoned Buildings and Dead Malls", "Historic Landmarks", "Action Films", "Documentaries "], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "Which actress married <PERSON> in 2000?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Celebrities", "question": "<PERSON><PERSON><PERSON> has a daughter named...?", "correct_answer": "Apple", "incorrect_answers": ["<PERSON>", "French", "Dakota"], "all_answers": ["<PERSON>", "French", "Apple", "Dakota"], "correct_answer_index": 2}], "Animals": [{"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What do you call a baby bat?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>ck", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>ck", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What is the scientific name for modern day humans?", "correct_answer": "Homo Sapiens", "incorrect_answers": ["<PERSON><PERSON> Ergaster", "Homo Erectus", "Homo <PERSON>eanderthalensis"], "all_answers": ["<PERSON><PERSON> Ergaster", "Homo Erectus", "Homo <PERSON>eanderthalensis", "Homo Sapiens"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What is the collective noun for a group of crows?", "correct_answer": "Murder", "incorrect_answers": ["Pack", "Gaggle", "Herd"], "all_answers": ["Murder", "Pack", "Gaggle", "Herd"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What is Grumpy <PERSON>&#039;s real name?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Sauce", "<PERSON>nie", "<PERSON><PERSON><PERSON><PERSON>"], "all_answers": ["Sauce", "<PERSON><PERSON><PERSON>", "<PERSON>nie", "<PERSON><PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What colour is the female blackbird?", "correct_answer": "<PERSON>", "incorrect_answers": ["Black", "White", "Yellow"], "all_answers": ["Black", "<PERSON>", "White", "Yellow"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "Snakes and lizards are known to flick their tongue, this behavior is to?", "correct_answer": "Capture scent particles", "incorrect_answers": ["Taste the sweet air", "Threaten other species", "Attract female mates"], "all_answers": ["Capture scent particles", "Taste the sweet air", "Threaten other species", "Attract female mates"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "What is the fastest  land animal?", "correct_answer": "Cheetah", "incorrect_answers": ["Lion", "Thomson&rsquo;s <PERSON><PERSON><PERSON>", "Pronghorn Antelope"], "all_answers": ["Lion", "Thomson&rsquo;s <PERSON><PERSON><PERSON>", "Pronghorn Antelope", "Cheetah"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "Which class of animals are newts members of?", "correct_answer": "Amphibian", "incorrect_answers": ["Fish", "Reptiles", "Mammals"], "all_answers": ["Amphibian", "Fish", "Reptiles", "Mammals"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "Hip<PERSON>cam<PERSON> is the Latin name for which marine creature?", "correct_answer": "Seahorse", "incorrect_answers": ["Dolphin", "Whale", "Octopus"], "all_answers": ["Dolphin", "Seahorse", "Whale", "Octopus"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Animals", "question": "By definition, where does an abyssopelagic animal live?", "correct_answer": "At the bottom of the ocean", "incorrect_answers": ["In the desert", "On top of a mountain", "Inside a tree"], "all_answers": ["In the desert", "On top of a mountain", "Inside a tree", "At the bottom of the ocean"], "correct_answer_index": 3}], "Vehicles": [{"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "The LS3 engine is how many cubic inches?", "correct_answer": "376", "incorrect_answers": ["346", "364", "427"], "all_answers": ["346", "364", "376", "427"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "What UK Train does NOT go over 125MPH?", "correct_answer": "Sprinter", "incorrect_answers": ["Class 43", "Javelin", "Pendolino"], "all_answers": ["Class 43", "Javelin", "Pendolino", "Sprinter"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "Which Italian city is home of the car manufacturer &#039;Fiat&#039;?", "correct_answer": "Turin", "incorrect_answers": ["Maranello", "Modena", "Rome"], "all_answers": ["Maranello", "Modena", "Turin", "Rome"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "The LS1 engine is how many cubic inches?", "correct_answer": "346", "incorrect_answers": ["350", "355", "360"], "all_answers": ["350", "355", "346", "360"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "Which car tire manufacturer is famous for its &quot;Eagle&quot; brand of tires, and is the official tire supplier of NASCAR?", "correct_answer": "Goodyear", "incorrect_answers": ["<PERSON><PERSON>", "Bridgestone", "<PERSON>in"], "all_answers": ["<PERSON><PERSON>", "Goodyear", "Bridgestone", "<PERSON>in"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "Which of the following collision avoidance systems helps airplanes avoid colliding with each other?", "correct_answer": "TCAS", "incorrect_answers": ["GPWS", "OCAS", "TAWS"], "all_answers": ["GPWS", "OCAS", "TAWS", "TCAS"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "The LS2 engine is how many cubic inches?", "correct_answer": "364", "incorrect_answers": ["346", "376", "402"], "all_answers": ["364", "346", "376", "402"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "The Italian automaker Lamborghini uses what animal as its logo?", "correct_answer": "Bull", "incorrect_answers": ["Bat", "Horse", "Snake"], "all_answers": ["Bull", "Bat", "Horse", "Snake"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "Which car tire manufacturer is famous for its &quot;P Zero&quot; line?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Goodyear", "Bridgestone", "<PERSON>in"], "all_answers": ["Goodyear", "<PERSON><PERSON>", "Bridgestone", "<PERSON>in"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Vehicles", "question": "What are the cylinder-like parts that pump up and down within the engine?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Leaf Springs", "Radiators", "ABS"], "all_answers": ["<PERSON><PERSON><PERSON>", "Leaf Springs", "Radiators", "ABS"], "correct_answer_index": 0}], "Entertainment: Comics": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "In &quot;Homestuck&quot; the &quot;Kingdom of Darkness&quot; is also known as?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Prospit", "The Medium"], "all_answers": ["<PERSON><PERSON><PERSON>", "Prospit", "<PERSON><PERSON>", "The Medium"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "In Black Hammer, what city did the heroes save from the Anti-God?", "correct_answer": "Spiral City", "incorrect_answers": ["Mega-City One", "Rockwood", "Star City"], "all_answers": ["Mega-City One", "Rockwood", "Star City", "Spiral City"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "Which one of these superhero teams appears in the Invincible comics?", "correct_answer": "Guardians of the Globe", "incorrect_answers": ["Avengers", "Justice League", "Teenage Mutant Ninja Turtles"], "all_answers": ["Guardians of the Globe", "Avengers", "Justice League", "Teenage Mutant Ninja Turtles"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "Who is <PERSON>?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "What is the alter-ego of the DC comics character &quot;Superman&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "Who is the creator of the comic series &quot;The Walking Dead&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "What is the full first name of the babysitter in <PERSON> and <PERSON><PERSON><PERSON>?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "What&#039;s the race of Invincible&#039;s father?", "correct_answer": "Viltrumite", "incorrect_answers": ["Kryptonian", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["Viltrumite", "Kryptonian", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "According to their longtime nickname, what kind of &quot;duo&quot; are <PERSON> &amp; <PERSON>?", "correct_answer": "Dynamic", "incorrect_answers": ["Dangerous", "Dynastic", "Delirious"], "all_answers": ["Dynamic", "Dangerous", "Dynastic", "Delirious"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Comics", "question": "In &quot;Homestuck&quot; what is <PERSON>&#039;s guardian?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Halley"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Halley"], "correct_answer_index": 2}], "Science: Gadgets": [{"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "The term &quot;battery&quot; to describe an electrical storage device was coined by?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", " <PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", " <PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "When was the iPhone released?", "correct_answer": "2007", "incorrect_answers": ["2005", "2006", "2004"], "all_answers": ["2007", "2005", "2006", "2004"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "What round is a classic AK-47 chambered in?", "correct_answer": "7.62x39mm", "incorrect_answers": ["7.62x51mm", "5.56x45mm", "5.45x39mm"], "all_answers": ["7.62x51mm", "7.62x39mm", "5.56x45mm", "5.45x39mm"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "When was the Tamagotchi digital pet released?", "correct_answer": "1996", "incorrect_answers": ["1989", "1992", "1990"], "all_answers": ["1996", "1989", "1992", "1990"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "When did the CD begin to appear on the consumer market?", "correct_answer": "1982", "incorrect_answers": ["1992", "1972", "1962"], "all_answers": ["1982", "1992", "1972", "1962"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "What does SSD stand for in the context of computer storage?", "correct_answer": " Solid State Drive", "incorrect_answers": ["Software Storage Device", "Super Speed Data", "Secure System Disk"], "all_answers": [" Solid State Drive", "Software Storage Device", "Super Speed Data", "Secure System Disk"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "What does GPS stand for?", "correct_answer": "Global Positioning System", "incorrect_answers": ["Global Personal System", "General Positioning System", "General Personal Satellite"], "all_answers": ["Global Positioning System", "Global Personal System", "General Positioning System", "General Personal Satellite"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "What is the name of a simple machine that is made up of a stiff arm that can move freely around a fixed point?", "correct_answer": "Lever", "incorrect_answers": ["Wedge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["Wedge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lever"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "Which of the following is not a type of computer mouse?", "correct_answer": "Smoothie mouse", "incorrect_answers": ["Drum mouse", "Trackball mouse", "Optical mouse"], "all_answers": ["Drum mouse", "Trackball mouse", "Smoothie mouse", "Optical mouse"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Science: Gadgets", "question": "Which gaming console is developed by Sony?", "correct_answer": "PlayStation", "incorrect_answers": ["Xbox", "Atari", "Nintendo Switch"], "all_answers": ["PlayStation", "Xbox", "Atari", "Nintendo Switch"], "correct_answer_index": 0}], "Entertainment: Japanese Anime & Manga": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In &quot;Fairy Tail&quot;, what is the nickname of <PERSON><PERSON>?", "correct_answer": "The Salamander", "incorrect_answers": ["The Dragon Slayer", "The Dragon", "The Demon"], "all_answers": ["The Dragon Slayer", "The Dragon", "The Salamander", "The Demon"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the author of the manga series &quot;Astro Boy&quot;?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What caused the titular mascot of Yo-Kai Watch, <PERSON><PERSON><PERSON>, to become a yokai?", "correct_answer": "Being run over by a truck", "incorrect_answers": ["Ate one too many chocobars", "Through a magical ritual", "When he put on the harmaki"], "all_answers": ["Ate one too many chocobars", "Through a magical ritual", "When he put on the harmaki", "Being run over by a truck"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In <PERSON><PERSON>&#039;s <PERSON>, who is <PERSON><PERSON>&#039;s maid?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Lucoa", "<PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["Lucoa", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the anime Seven Deadly Sins what is the name of one of the sins?", "correct_answer": "<PERSON>", "incorrect_answers": ["Sakura", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "all_answers": ["Sakura", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "<PERSON><PERSON> and <PERSON><PERSON> are main characters from which anime?", "correct_answer": "Sword Art Online", "incorrect_answers": ["One Piece", "Death Note", "Fairy Tail"], "all_answers": ["One Piece", "Death Note", "Sword Art Online", "Fairy Tail"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In &quot;A Certain Scientific Railgun&quot;, how many &quot;sisters&quot; did Accelerator have to kill to achieve the rumored level 6?", "correct_answer": "20,000", "incorrect_answers": ["128", "10,000", "5,000"], "all_answers": ["128", "10,000", "20,000", "5,000"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In Digimon, what is the Japanese name for the final evolutionary stage?", "correct_answer": "Ultimate", "incorrect_answers": ["Mega", "Adult", "Champion"], "all_answers": ["Mega", "Ultimate", "Adult", "Champion"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In 2013, virtual pop-star <PERSON><PERSON><PERSON> had a sponsorship with which pizza chain?", "correct_answer": "Domino&#039;s", "incorrect_answers": ["<PERSON>&#039;s", "Pizza Hut", "Sabarro&#039;s"], "all_answers": ["<PERSON>&#039;s", "Pizza Hut", "Domino&#039;s", "Sabarro&#039;s"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In &quot;The Melancholy of <PERSON><PERSON><PERSON>&quot; series, the SOS Brigade club leader is unknowingly treated as a(n) __ by her peers.", "correct_answer": "God", "incorrect_answers": ["Alien", "Time Traveler", "<PERSON><PERSON>"], "all_answers": ["God", "Alien", "Time Traveler", "<PERSON><PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the anime <PERSON><PERSON> who is one of the main protagonists?", "correct_answer": "Yu<PERSON>e", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Mine<PERSON>", "<PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "Mine<PERSON>", "<PERSON><PERSON>", "Yu<PERSON>e"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In the 2012 animated film &quot;Wolf Children&quot;, what are the names of the wolf children?", "correct_answer": "Ame &amp; <PERSON><PERSON>", "incorrect_answers": ["Hana &amp; <PERSON><PERSON>", "Ame &amp; Hana", "Chuck &amp; Anna"], "all_answers": ["Hana &amp; <PERSON><PERSON>", "Ame &amp; <PERSON><PERSON>", "Ame &amp; Hana", "Chuck &amp; Anna"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "The name of <PERSON><PERSON>&#039;s imposter at the beginning of Danganronpa: Trigger Happy Havoc is?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON><PERSON>", "Ultimate Imposter", "<PERSON><PERSON><PERSON>"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ultimate Imposter", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "<PERSON><PERSON><PERSON> in &quot;Re:Zero&quot; is the witch of what?", "correct_answer": "Envy", "incorrect_answers": ["Pride", "<PERSON><PERSON><PERSON>", "Wrath"], "all_answers": ["Pride", "<PERSON><PERSON><PERSON>", "Wrath", "Envy"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "In &quot;<PERSON><PERSON><PERSON><PERSON>&quot;, what are the heros are looking to collect?", "correct_answer": "Jewel Shards", "incorrect_answers": ["Dragon Balls", "<PERSON><PERSON>", "Sacred Stones"], "all_answers": ["Dragon Balls", "Jewel Shards", "<PERSON><PERSON>", "Sacred Stones"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What color is <PERSON><PERSON><PERSON><PERSON>&#039;s half of the Scissor Blade in the anime Kill la KIll?", "correct_answer": "Red", "incorrect_answers": ["Green", "Blue", "Purple"], "all_answers": ["Green", "Blue", "Purple", "Red"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Which anime heavily features music from the genre &quot;Eurobeat&quot;?", "correct_answer": "Initial D", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON><PERSON> no <PERSON>bi", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON><PERSON> no <PERSON>bi", "Initial D", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What&#039;s the English Dub Name of &quot;Smile Precure&quot;?", "correct_answer": "Glitter Force", "incorrect_answers": ["Sparkle Girls", "Fairy Tale Patrol", "Power Princesses"], "all_answers": ["Sparkle Girls", "Fairy Tale Patrol", "Power Princesses", "Glitter Force"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "What name is the main character <PERSON><PERSON> given in the 2001 movie &quot;Spirited Away&quot;?", "correct_answer": "<PERSON> (Thousand)", "incorrect_answers": ["Hyaku (Hundred)", "<PERSON>chiman (Ten thousand)", "<PERSON><PERSON> (Ten)"], "all_answers": ["<PERSON> (Thousand)", "Hyaku (Hundred)", "<PERSON>chiman (Ten thousand)", "<PERSON><PERSON> (Ten)"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Japanese Anime &amp; Manga", "question": "Who is the main heroine of the anime, Full Metal Panic!", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["Teletha Testarossa", "<PERSON>", "<PERSON><PERSON><PERSON>"], "all_answers": ["Teletha Testarossa", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "correct_answer_index": 2}], "Entertainment: Cartoon & Animations": [{"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the relationship between <PERSON> and <PERSON><PERSON><PERSON> in the show &quot;<PERSON> and <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "Grandfather and Grandson", "incorrect_answers": ["Father and Son", "Best Friends", "Crimefighting Partners"], "all_answers": ["Grandfather and Grandson", "Father and Son", "Best Friends", "Crimefighting Partners"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the Pixar film, &quot;Toy Story&quot; what was the name of the child who owned the toys?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who is the only voice actor to have a speaking part in all of the Disney Pixar feature films? ", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the name of the city that The Flintstones is based in?", "correct_answer": "Bedrock", "incorrect_answers": ["Stoneville", "Rockhampton", "Boulder City"], "all_answers": ["Stoneville", "Bedrock", "Rockhampton", "Boulder City"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What was the name of the sea witch in the 1989 Disney film &quot;The Little Mermaid&quot;?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "Maleficent", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON><PERSON>", "Maleficent", "<PERSON>"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the show &quot;<PERSON>&quot;, who are the main two employees of The Big Donut?", "correct_answer": "<PERSON> and <PERSON>", "incorrect_answers": ["<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>"], "all_answers": ["<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>", "<PERSON> and <PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which &#039;Family Guy&#039; character got his own spin-off show in 2009?", "correct_answer": "<PERSON> Brown", "incorrect_answers": ["<PERSON>", "<PERSON>", "The Greased-up <PERSON><PERSON> Guy"], "all_answers": ["<PERSON>", "<PERSON>", "The Greased-up <PERSON><PERSON> Guy", "<PERSON> Brown"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the name of the creatures that the protagonists of the webshow RWBY fight against?", "correct_answer": "<PERSON>", "incorrect_answers": ["Reavers", "Heartless", "Dark Ones"], "all_answers": ["Reavers", "Heartless", "Dark Ones", "<PERSON>"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In the show &quot;Foster&#039;s Home For Imaginary Friends&quot;, which character had an obsession with basketball?", "correct_answer": "Wilt", "incorrect_answers": ["Coco", "<PERSON>", "Cheese"], "all_answers": ["Coco", "<PERSON>", "Cheese", "Wilt"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who was the villain of &#039;&#039;The Lion King&#039;&#039;?", "correct_answer": "Scar", "incorrect_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vada"], "all_answers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vada", "Scar"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of the following is not a Flintstones character?", "correct_answer": "<PERSON>", "incorrect_answers": ["Rockhead Slate", "The Great Gazoo", "<PERSON>"], "all_answers": ["Rockhead Slate", "The Great Gazoo", "<PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What was the first Disney movie to use CGI?", "correct_answer": "The Black Cauldron", "incorrect_answers": ["Tron", "Toy Story", "Fantasia"], "all_answers": ["The Black Cauldron", "Tron", "Toy Story", "Fantasia"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of these characters live in a pineapple under the sea in the cartoon &quot;SpongeBob SquarePants&quot;.", "correct_answer": "SpongeBob SquarePants ", "incorrect_answers": ["<PERSON>", "Squidward Tentacles", "Mr. <PERSON>"], "all_answers": ["<PERSON>", "Squidward Tentacles", "Mr. <PERSON>", "SpongeBob SquarePants "], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is lost in Hawaiian and is also the name of a little girl in a 2002 film which features a alien named &quot;Stitch&quot;?", "correct_answer": "<PERSON><PERSON>", "incorrect_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correct_answer_index": 2}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "What is the surname of one of the male teachers in the BBC series Postman Pat?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["<PERSON>", "Do<PERSON>", "Lays"], "all_answers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Do<PERSON>", "Lays"], "correct_answer_index": 0}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "In &quot;Avatar: The Last Airbender&quot;, which element does <PERSON><PERSON> begin to learn after being defrosted?", "correct_answer": "Water", "incorrect_answers": ["Air", "Earth", "Fire"], "all_answers": ["Air", "Earth", "Fire", "Water"], "correct_answer_index": 3}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of these is NOT a catchphrase used by <PERSON> in the TV show &quot;<PERSON> and <PERSON><PERSON><PERSON>&quot;?", "correct_answer": "Slam dunk, nothing but net!", "incorrect_answers": ["Hit the sack, <PERSON>!", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>, bi<PERSON>!", "Wubba-lubba-dub-dub!"], "all_answers": ["Hit the sack, <PERSON>!", "Slam dunk, nothing but net!", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>, bi<PERSON>!", "Wubba-lubba-dub-dub!"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Which of the following did not feature in the cartoon &#039;Wacky Races&#039;?", "correct_answer": "The Dragon Wagon", "incorrect_answers": ["The Bouldermobile", "The Crimson Haybailer", "The Compact Pussycat"], "all_answers": ["The Bouldermobile", "The Dragon Wagon", "The Crimson Haybailer", "The Compact Pussycat"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "Who voices for <PERSON> in the animated series RWBY?", "correct_answer": "<PERSON>", "incorrect_answers": ["<PERSON>", "<PERSON>", "<PERSON>"], "all_answers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correct_answer_index": 1}, {"type": "multiple", "difficulty": "easy", "category": "Entertainment: Cartoon &amp; Animations", "question": "<PERSON> appeared in the Super Mario DIC Cartoons, but what was she known as?", "correct_answer": "<PERSON><PERSON><PERSON>", "incorrect_answers": ["Sweetie Pie", "<PERSON>", "Honey Pie"], "all_answers": ["<PERSON><PERSON><PERSON>", "Sweetie Pie", "<PERSON>", "Honey Pie"], "correct_answer_index": 0}]}