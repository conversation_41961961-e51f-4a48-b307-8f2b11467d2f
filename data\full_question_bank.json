{"Movie Trivia": [{"question": "Who directed the movie 'Titanic'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "A"}, {"question": "Which film won Best Picture at the Oscars in 2020?", "options": {"A": "Joker", "B": "1917", "C": "Parasite", "D": "Ford v Ferrari"}, "answer": "C"}, {"question": "What is the highest - grossing film of all time (worldwide)?", "options": {"A": "Avatar", "B": "Avengers: Endgame", "C": "Titanic", "D": "Star Wars: The Force Awakens"}, "answer": "A"}], "Zodiac Signs": [{"question": "Which sign is symbolized by the <PERSON>?", "options": {"A": "<PERSON><PERSON>", "B": "<PERSON>", "C": "<PERSON><PERSON><PERSON><PERSON>", "D": "Capricorn"}, "answer": "A"}, {"question": "What element is associated with Taurus?", "options": {"A": "Air", "B": "Fire", "C": "Earth", "D": "Water"}, "answer": "C"}], "Law and Legal Knowledge": [{"question": "Which document is the supreme law of the United States?", "options": {"A": "Bill of Rights", "B": "Declaration of Independence", "C": "Constitution", "D": "Federal Register"}, "answer": "C"}, {"question": "What is the legal term for written false statements?", "options": {"A": "Libel", "B": "Slander", "C": "Perjury", "D": "Defamation"}, "answer": "A"}], "U.S. History": [{"question": "In which year did the U.S. declare independence?", "options": {"A": "1776", "B": "1789", "C": "1801", "D": "1754"}, "answer": "A"}, {"question": "Who was the first President of the United States?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "B"}], "World Geography": [{"question": "Which is the largest desert in the world?", "options": {"A": "Gobi", "B": "Sahara", "C": "Antarctic Desert", "D": "Arabian Desert"}, "answer": "C"}, {"question": "Which country has the most islands?", "options": {"A": "Philippines", "B": "Indonesia", "C": "Sweden", "D": "Canada"}, "answer": "C"}], "Space & Astronomy": [{"question": "Which planet is known as the Red Planet?", "options": {"A": "Mars", "B": "Venus", "C": "Jupiter", "D": "Saturn"}, "answer": "A"}, {"question": "How many planets are in our solar system?", "options": {"A": "8", "B": "9", "C": "10", "D": "7"}, "answer": "A"}, {"question": "What is the name of our galaxy?", "options": {"A": "Andromeda", "B": "Milky Way", "C": "Sombrero", "D": "Whirlpool"}, "answer": "B"}], "Famous People": [{"question": "Who is known for painting the Mona <PERSON>?", "options": {"A": "<PERSON><PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "C"}, {"question": "Who was the first man on the Moon?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "C"}], "U.S. Presidents": [{"question": "Who was the 16th President of the United States?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "A"}, {"question": "Which President resigned due to the Watergate scandal?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "A"}], "Food & Cuisine": [{"question": "Which country is known for sushi?", "options": {"A": "China", "B": "Japan", "C": "Korea", "D": "Thailand"}, "answer": "B"}, {"question": "What is the main ingredient in guacamole?", "options": {"A": "Avocado", "B": "Tomato", "C": "<PERSON><PERSON><PERSON>ber", "D": "Onion"}, "answer": "A"}], "Capital Cities": [{"question": "What is the capital of Canada?", "options": {"A": "Toronto", "B": "Ottawa", "C": "Vancouver", "D": "Montreal"}, "answer": "B"}, {"question": "What is the capital of Australia?", "options": {"A": "Sydney", "B": "Melbourne", "C": "Canberra", "D": "Brisbane"}, "answer": "C"}], "Music Knowledge": [{"question": "Who is known as the 'King of Pop'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "Prince", "D": "<PERSON>"}, "answer": "B"}, {"question": "Which band released the album 'Abbey Road'?", "options": {"A": "The Rolling Stones", "B": "The Beatles", "C": "<PERSON>", "D": "Led Zeppelin"}, "answer": "B"}], "Literature Classics": [{"question": "Who wrote 'Pride and Prejudice'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "A"}, {"question": "In which novel does the character <PERSON><PERSON> appear?", "options": {"A": "The Great Gatsby", "B": "To Kill a Mockingbird", "C": "<PERSON><PERSON>", "D": "1984"}, "answer": "B"}], "Spelling Test": [{"question": "Choose the correct spelling:", "options": {"A": "Accomodate", "B": "Acommodate", "C": "Accommodate", "D": "Acomodate"}, "answer": "C"}, {"question": "Choose the correct spelling:", "options": {"A": "Definately", "B": "Definitely", "C": "Definetly", "D": "Defanitely"}, "answer": "B"}], "Grammar Challenge": [{"question": "Which sentence is correct?", "options": {"A": "She don't like apples.", "B": "She doesn't likes apples.", "C": "She doesn't like apples.", "D": "She don't likes apples."}, "answer": "C"}, {"question": "Identify the verb in this sentence: 'They are running fast.'", "options": {"A": "They", "B": "are", "C": "running", "D": "fast"}, "answer": "C"}], "Animal Facts": [{"question": "Which mammal is known to have the longest gestation period?", "options": {"A": "Elephant", "B": "Blue Whale", "C": "Giraffe", "D": "Horse"}, "answer": "A"}, {"question": "What is a group of lions called?", "options": {"A": "Pack", "B": "Pride", "C": "Herd", "D": "Flock"}, "answer": "B"}], "Ocean & Marine Life": [{"question": "What is the largest species of shark?", "options": {"A": "Great White Shark", "B": "Hammerhead Shark", "C": "Whale Shark", "D": "Tiger Shark"}, "answer": "C"}, {"question": "Which marine animal has eight legs?", "options": {"A": "<PERSON><PERSON>", "B": "Lobster", "C": "Octopus", "D": "Squid"}, "answer": "C"}], "Plants & Trees": [{"question": "Which tree produces acorns?", "options": {"A": "Maple", "B": "Oak", "C": "Pine", "D": "<PERSON>"}, "answer": "B"}, {"question": "What part of the plant conducts photosynthesis?", "options": {"A": "Root", "B": "<PERSON><PERSON>", "C": "Leaf", "D": "Flower"}, "answer": "C"}], "Mythology": [{"question": "Who is the Greek god of the sea?", "options": {"A": "Zeus", "B": "Hades", "C": "Poseidon", "D": "Apollo"}, "answer": "C"}, {"question": "In Norse mythology, who wields the hammer <PERSON><PERSON><PERSON><PERSON>?", "options": {"A": "<PERSON><PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON><PERSON>"}, "answer": "B"}], "Internet Slang": [{"question": "What does 'LOL' stand for?", "options": {"A": "Lots of Love", "B": "Laugh Out Loud", "C": "Leave Our Location", "D": "Lack of Logic"}, "answer": "B"}, {"question": "What is the meaning of 'BRB'?", "options": {"A": "Be Right Back", "B": "Big Red <PERSON>", "C": "Better Run Back", "D": "Bring Real Bacon"}, "answer": "A"}], "Social Media Trends": [{"question": "Which platform is known for 'tweets'?", "options": {"A": "Facebook", "B": "Instagram", "C": "Twitter", "D": "LinkedIn"}, "answer": "C"}, {"question": "What does 'hashtag' (#) do on social media?", "options": {"A": "Likes a post", "B": "Categorizes content", "C": "Blocks users", "D": "Sends private message"}, "answer": "B"}], "Sports Trivia (General)": [{"question": "How many players are on a soccer team on the field?", "options": {"A": "10", "B": "11", "C": "12", "D": "9"}, "answer": "B"}, {"question": "In which sport is the term 'home run' used?", "options": {"A": "Cricket", "B": "Baseball", "C": "Football", "D": "Hockey"}, "answer": "B"}], "NBA Trivia": [{"question": "Who has the most NBA championships as a player?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON><PERSON><PERSON>"}, "answer": "A"}, {"question": "Which NBA team is known as the 'Lakers'?", "options": {"A": "Los Angeles", "B": "Chicago", "C": "Miami", "D": "Boston"}, "answer": "A"}], "Soccer/Football Facts": [{"question": "Which country won the FIFA World Cup in 2018?", "options": {"A": "Brazil", "B": "Germany", "C": "France", "D": "Argentina"}, "answer": "C"}, {"question": "How many minutes are there in a standard soccer match?", "options": {"A": "90", "B": "60", "C": "120", "D": "75"}, "answer": "A"}], "Olympic Games": [{"question": "In which city were the first modern Olympic Games held?", "options": {"A": "Athens", "B": "Paris", "C": "London", "D": "Rome"}, "answer": "A"}, {"question": "How often are the Summer Olympics held?", "options": {"A": "Every 2 years", "B": "Every 4 years", "C": "Every 3 years", "D": "Every 5 years"}, "answer": "B"}], "Video Games": [{"question": "Which video game features characters like <PERSON> and <PERSON>?", "options": {"A": "<PERSON> the Hedgehog", "B": "The Legend of <PERSON><PERSON>a", "C": "Super Mario Bros.", "D": "Pac-Man"}, "answer": "C"}, {"question": "What is the best-selling video game of all time?", "options": {"A": "Minecraft", "B": "<PERSON><PERSON><PERSON>", "C": "Grand Theft Auto V", "D": "Wii Sports"}, "answer": "A"}], "Board Games": [{"question": "In which board game do players buy properties and collect rent?", "options": {"A": "Monopoly", "B": "Chess", "C": "Scrabble", "D": "Risk"}, "answer": "A"}, {"question": "Which game uses pieces called 'meeples'?", "options": {"A": "Settlers of Catan", "B": "Ticket to Ride", "C": "Clue", "D": "Pandemic"}, "answer": "A"}], "Harry Potter": [{"question": "What is the name of <PERSON>’s pet owl?", "options": {"A": "<PERSON><PERSON><PERSON>", "B": "Crookshanks", "C": "Scabbers", "D": "<PERSON><PERSON><PERSON>"}, "answer": "A"}, {"question": "Which house at Hogwarts does <PERSON> belong to?", "options": {"A": "<PERSON><PERSON><PERSON><PERSON>", "B": "<PERSON><PERSON><PERSON><PERSON>", "C": "Raven<PERSON>law", "D": "<PERSON><PERSON><PERSON><PERSON>"}, "answer": "D"}], "Romantic Comedies": [{"question": "Which movie features the phrase 'You had me at hello'?", "options": {"A": "Pretty Woman", "B": "<PERSON>", "C": "Notting Hill", "D": "10 Things I Hate About You"}, "answer": "B"}, {"question": "Who starred in 'When Harry Met Sally'?", "options": {"A": "<PERSON> and <PERSON>", "B": "<PERSON> and <PERSON>", "C": "<PERSON> and <PERSON>", "D": "<PERSON> and <PERSON>"}, "answer": "A"}], "Disney Movies": [{"question": "Who is the main character in 'The Lion King'?", "options": {"A": "Simba", "B": "<PERSON><PERSON><PERSON>", "C": "Scar", "D": "<PERSON><PERSON>"}, "answer": "A"}, {"question": "Which Disney princess loses her glass slipper?", "options": {"A": "<PERSON>", "B": "Cinderella", "C": "Aurora", "D": "<PERSON>"}, "answer": "B"}], "Horror Movies": [{"question": "Who is the villain in the movie 'Halloween'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON><PERSON>"}, "answer": "C"}, {"question": "Which horror movie features the phrase 'Here's Johnny!'?", "options": {"A": "The Shining", "B": "It", "C": "Scream", "D": "The Exorcist"}, "answer": "A"}], "Cartoon Characters": [{"question": "What is the name of the rabbit in 'Looney Tunes'?", "options": {"A": "<PERSON>", "B": "<PERSON><PERSON>", "C": "Tweety", "D": "Porky Pig"}, "answer": "A"}, {"question": "Which cartoon character lives in a pineapple under the sea?", "options": {"A": "SpongeBob SquarePants", "B": "<PERSON>", "C": "Squidward", "D": "Mr. <PERSON>"}, "answer": "A"}], "Marvel Universe": [{"question": "Who is known as the 'First Avenger'?", "options": {"A": "Iron Man", "B": "Captain <PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "B"}, {"question": "What is the name of <PERSON>’s hammer?", "options": {"A": "Stormbreaker", "B": "<PERSON><PERSON><PERSON><PERSON>", "C": "<PERSON><PERSON><PERSON>", "D": "Thunderstrike"}, "answer": "B"}], "DC Comics": [{"question": "Who is the alter ego of <PERSON>?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "B"}, {"question": "Which superhero is known as the 'Man of Steel'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "Green Lantern", "D": "Flash"}, "answer": "B"}], "Anime & Manga": [{"question": "Which anime features the character '<PERSON><PERSON><PERSON>'?", "options": {"A": "Dragon Ball", "B": "<PERSON><PERSON><PERSON>", "C": "One Piece", "D": "Bleach"}, "answer": "B"}, {"question": "What is the term for Japanese comics?", "options": {"A": "Manhwa", "B": "Manga", "C": "Manhua", "D": "Anime"}, "answer": "B"}], "Mental Health Awareness": [{"question": "What is a common symptom of depression?", "options": {"A": "Increased energy", "B": "Persistent sadness", "C": "Improved concentration", "D": "Euphoria"}, "answer": "B"}, {"question": "Which therapy involves talking to a mental health professional?", "options": {"A": "Chemotherapy", "B": "Psychotherapy", "C": "Radiation therapy", "D": "Physical therapy"}, "answer": "B"}], "World Religions": [{"question": "Which religion follows the teachings of Buddha?", "options": {"A": "Hinduism", "B": "Christianity", "C": "Buddhism", "D": "Islam"}, "answer": "C"}, {"question": "What is the holy book of Islam?", "options": {"A": "Bible", "B": "Quran", "C": "Torah", "D": "<PERSON><PERSON><PERSON>"}, "answer": "B"}], "Environmental Science": [{"question": "What gas do plants absorb during photosynthesis?", "options": {"A": "Oxygen", "B": "Carbon Dioxide", "C": "Nitrogen", "D": "Methane"}, "answer": "B"}, {"question": "Which layer protects the Earth from harmful UV rays?", "options": {"A": "Ozone layer", "B": "Troposphere", "C": "Stratosphere", "D": "Mesosphere"}, "answer": "A"}], "Climate Change": [{"question": "What is the main greenhouse gas responsible for global warming?", "options": {"A": "Oxygen", "B": "Carbon Dioxide", "C": "Nitrogen", "D": "Helium"}, "answer": "B"}, {"question": "Which international agreement aims to reduce carbon emissions?", "options": {"A": "Kyoto Protocol", "B": "Geneva Convention", "C": "Paris Agreement", "D": "Montreal Protocol"}, "answer": "C"}], "Renewable Energy": [{"question": "Which of these is a renewable energy source?", "options": {"A": "Coal", "B": "Natural Gas", "C": "Solar Power", "D": "Oil"}, "answer": "C"}, {"question": "What device converts sunlight into electricity?", "options": {"A": "Generator", "B": "Solar Panel", "C": "Battery", "D": "Transformer"}, "answer": "B"}], "Chemistry Basics": [{"question": "What is the chemical symbol for water?", "options": {"A": "O2", "B": "CO2", "C": "H2O", "D": "NaCl"}, "answer": "C"}, {"question": "What is the pH level of pure water?", "options": {"A": "7", "B": "0", "C": "14", "D": "1"}, "answer": "A"}], "Physics Fundamentals": [{"question": "What is the unit of force?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "Joule", "D": "<PERSON>"}, "answer": "B"}, {"question": "What is the speed of light?", "options": {"A": "300,000 km/s", "B": "150,000 km/s", "C": "299,792 km/s", "D": "3,000 km/s"}, "answer": "C"}], "Mathematics": [{"question": "What is the value of π (pi) rounded to two decimals?", "options": {"A": "3.12", "B": "3.14", "C": "3.15", "D": "3.13"}, "answer": "B"}, {"question": "What is 7 multiplied by 8?", "options": {"A": "54", "B": "56", "C": "58", "D": "52"}, "answer": "B"}], "Algebra": [{"question": "Solve for x: 2x + 3 = 7", "options": {"A": "1", "B": "2", "C": "3", "D": "4"}, "answer": "B"}, {"question": "What is the quadratic formula used for?", "options": {"A": "Solving linear equations", "B": "Solving quadratic equations", "C": "Calculating area", "D": "Finding slope"}, "answer": "B"}], "Geometry": [{"question": "How many sides does a hexagon have?", "options": {"A": "5", "B": "6", "C": "7", "D": "8"}, "answer": "B"}, {"question": "What is the sum of the angles in a triangle?", "options": {"A": "180 degrees", "B": "90 degrees", "C": "360 degrees", "D": "270 degrees"}, "answer": "A"}], "Calculus": [{"question": "What is the derivative of a constant?", "options": {"A": "Zero", "B": "One", "C": "The constant itself", "D": "Undefined"}, "answer": "A"}, {"question": "What does the integral symbol ∫ represent?", "options": {"A": "Derivative", "B": "Summation", "C": "Integration", "D": "Limit"}, "answer": "C"}], "English Vocabulary": [{"question": "What does 'benevolent' mean?", "options": {"A": "Kind and generous", "B": "Angry", "C": "Lazy", "D": "Strong"}, "answer": "A"}, {"question": "What is a synonym for 'happy'?", "options": {"A": "Sad", "B": "Joyful", "C": "Angry", "D": "Tired"}, "answer": "B"}], "Famous Quotes": [{"question": "Who said, 'I think, therefore I am'?", "options": {"A": "<PERSON>", "B": "<PERSON><PERSON><PERSON>", "C": "<PERSON>", "D": "Socrates"}, "answer": "B"}, {"question": "Who said, 'The only thing we have to fear is fear itself'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "B"}], "World Capitals": [{"question": "What is the capital of Canada?", "options": {"A": "Toronto", "B": "Ottawa", "C": "Vancouver", "D": "Montreal"}, "answer": "B"}, {"question": "What is the capital of Australia?", "options": {"A": "Sydney", "B": "Melbourne", "C": "Canberra", "D": "Brisbane"}, "answer": "C"}], "Famous Landmarks": [{"question": "Where is the Eiffel Tower located?", "options": {"A": "London", "B": "Paris", "C": "Rome", "D": "Berlin"}, "answer": "B"}, {"question": "What is the Great Wall of China primarily built for?", "options": {"A": "Tourism", "B": "Defense", "C": "Trade", "D": "Religious ceremonies"}, "answer": "B"}], "Weather & Climate": [{"question": "What instrument measures air pressure?", "options": {"A": "Thermometer", "B": "Barometer", "C": "Hygrometer", "D": "Anemometer"}, "answer": "B"}, {"question": "What type of cloud is fluffy and white?", "options": {"A": "<PERSON><PERSON><PERSON>", "B": "Cumulus", "C": "Cirrus", "D": "Nimbus"}, "answer": "B"}], "Cars & Automobiles": [{"question": "Who is the founder of Tesla, Inc.?", "options": {"A": "<PERSON><PERSON>", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON><PERSON><PERSON> Honda"}, "answer": "A"}, {"question": "What does SUV stand for?", "options": {"A": "Standard Utility Vehicle", "B": "Sport Utility Vehicle", "C": "Small Urban Vehicle", "D": "Super Utility Van"}, "answer": "B"}], "Fashion & Style": [{"question": "Which city is considered the fashion capital of the world?", "options": {"A": "New York", "B": "Paris", "C": "Milan", "D": "London"}, "answer": "B"}, {"question": "What fabric is made from flax plants?", "options": {"A": "Cotton", "B": "Linen", "C": "Silk", "D": "Wool"}, "answer": "B"}], "Food & Cooking": [{"question": "Which country is famous for sushi?", "options": {"A": "China", "B": "Japan", "C": "Korea", "D": "Thailand"}, "answer": "B"}, {"question": "What ingredient makes bread rise?", "options": {"A": "Salt", "B": "Yeast", "C": "Sugar", "D": "Flour"}, "answer": "B"}], "World Flags": [{"question": "Which country's flag has a red maple leaf?", "options": {"A": "United States", "B": "Canada", "C": "Australia", "D": "United Kingdom"}, "answer": "B"}, {"question": "Which flag has horizontal red and white stripes?", "options": {"A": "United States", "B": "Malaysia", "C": "Austria", "D": "Poland"}, "answer": "C"}], "Musical Instruments": [{"question": "Which instrument has 88 keys?", "options": {"A": "Guitar", "B": "Piano", "C": "Violin", "D": "Flute"}, "answer": "B"}, {"question": "Which instrument is known as a string instrument?", "options": {"A": "Drums", "B": "Trumpet", "C": "Violin", "D": "Flute"}, "answer": "C"}], "Classical Music": [{"question": "Who composed the 'Fifth Symphony'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "Bach", "D": "<PERSON><PERSON>"}, "answer": "B"}, {"question": "What instrument is often associated with Mozart?", "options": {"A": "Piano", "B": "Guitar", "C": "Violin", "D": "Cello"}, "answer": "A"}], "Modern Music": [{"question": "Which artist is known as the 'King of Pop'?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "Prince", "D": "<PERSON>"}, "answer": "B"}, {"question": "What genre is Taylor <PERSON> primarily associated with?", "options": {"A": "Pop", "B": "Country", "C": "Rock", "D": "Jazz"}, "answer": "A"}], "World Languages": [{"question": "Which language has the most native speakers?", "options": {"A": "English", "B": "Mandarin Chinese", "C": "Spanish", "D": "Hindi"}, "answer": "B"}, {"question": "What language uses the Cyrillic alphabet?", "options": {"A": "Greek", "B": "Russian", "C": "Arabic", "D": "Hebrew"}, "answer": "B"}], "Cybersecurity Basics": [{"question": "What does 'phishing' refer to in cybersecurity?", "options": {"A": "A type of malware", "B": "A fraudulent attempt to obtain sensitive information", "C": "A network protocol", "D": "A password manager"}, "answer": "B"}, {"question": "What is a firewall used for?", "options": {"A": "To cool down a computer", "B": "To block unauthorized access", "C": "To speed up the internet", "D": "To store passwords"}, "answer": "B"}], "Cryptocurrencies": [{"question": "Which cryptocurrency is the first and most well - known?", "options": {"A": "Ethereum", "B": "Bitcoin", "C": "<PERSON><PERSON><PERSON>", "D": "Litecoin"}, "answer": "B"}, {"question": "What technology underlies cryptocurrencies?", "options": {"A": "Artificial Intelligence", "B": "Blockchain", "C": "Cloud Computing", "D": "Virtual Reality"}, "answer": "B"}], "Economics 101": [{"question": "What does GDP stand for?", "options": {"A": "Gross Domestic Product", "B": "General Debt Payment", "C": "Gross Debt Percentage", "D": "General Domestic Price"}, "answer": "A"}, {"question": "What is the law of supply and demand?", "options": {"A": "Price decreases with demand", "B": "Price increases with supply", "C": "Price depends on supply and demand", "D": "Supply always exceeds demand"}, "answer": "C"}], "Business Terms": [{"question": "What is a startup?", "options": {"A": "A well - established company", "B": "A new business venture", "C": "A government organization", "D": "A nonprofit"}, "answer": "B"}, {"question": "What does ROI stand for?", "options": {"A": "Return on Investment", "B": "Rate of Interest", "C": "Revenue on Income", "D": "Risk of Inflation"}, "answer": "A"}], "Marketing & Advertising": [{"question": "What does SEO stand for?", "options": {"A": "Search Engine Optimization", "B": "Social Engagement Outreach", "C": "Sales Effective Operations", "D": "Standard Event Organization"}, "answer": "A"}, {"question": "What is a 'brand'?", "options": {"A": "A product logo", "B": "A company's identity", "C": "A marketing slogan", "D": "An advertisement"}, "answer": "B"}], "Entrepreneurship": [{"question": "What is a business plan?", "options": {"A": "A financial report", "B": "A roadmap for a business", "C": "A marketing strategy", "D": "A customer database"}, "answer": "B"}, {"question": "What is venture capital?", "options": {"A": "A loan from a bank", "B": "Investment for startups", "C": "Government funding", "D": "Crowdfunding"}, "answer": "B"}], "Financial Literacy": [{"question": "What is a credit score?", "options": {"A": "A measure of financial trustworthiness", "B": "A bank account balance", "C": "A loan amount", "D": "A type of savings"}, "answer": "A"}, {"question": "What does APR stand for?", "options": {"A": "Annual Percentage Rate", "B": "Account Payment Rate", "C": "Average Profit Ratio", "D": "Annual Profit Rate"}, "answer": "A"}], "Stock Market Basics": [{"question": "What is a stock?", "options": {"A": "A loan to a company", "B": "Ownership in a company", "C": "A type of bond", "D": "A government security"}, "answer": "B"}, {"question": "What does IPO stand for?", "options": {"A": "Initial Public Offering", "B": "International Purchase Order", "C": "Investment Portfolio Option", "D": "Internal Price Offering"}, "answer": "A"}], "Banking Terms": [{"question": "What is an ATM?", "options": {"A": "Automated Teller Machine", "B": "Account Transaction Method", "C": "Automated Transfer Mechanism", "D": "Annual Tax Management"}, "answer": "A"}, {"question": "What is a mortgage?", "options": {"A": "A credit card", "B": "A home loan", "C": "A savings account", "D": "A type of insurance"}, "answer": "B"}], "Travel & Tourism": [{"question": "Which city is known as the 'City of Light'?", "options": {"A": "Paris", "B": "New York", "C": "London", "D": "Rome"}, "answer": "A"}, {"question": "What currency is used in Japan?", "options": {"A": "Yuan", "B": "Yen", "C": "Won", "D": "Dollar"}, "answer": "B"}], "U.S. States Trivia": [{"question": "What is the capital of California?", "options": {"A": "Los Angeles", "B": "Sacramento", "C": "San Francisco", "D": "San Diego"}, "answer": "B"}, {"question": "Which U.S. state is known as the 'Sunshine State'?", "options": {"A": "California", "B": "Florida", "C": "Texas", "D": "Arizona"}, "answer": "B"}], "European History": [{"question": "Who was the first Holy Roman Emperor?", "options": {"A": "Charlemagne", "B": "<PERSON>", "C": "<PERSON>", "D": "<PERSON>"}, "answer": "A"}, {"question": "In which year did World War II end?", "options": {"A": "1945", "B": "1939", "C": "1918", "D": "1950"}, "answer": "A"}], "Asian Culture": [{"question": "Which festival is known as the 'Festival of Lights' in India?", "options": {"A": "<PERSON><PERSON>", "B": "<PERSON><PERSON><PERSON>", "C": "Lunar New Year", "D": "Obon"}, "answer": "B"}, {"question": "What is the traditional Japanese garment called?", "options": {"A": "Hanbok", "B": "<PERSON><PERSON>", "C": "<PERSON><PERSON>", "D": "Cheongsam"}, "answer": "B"}], "African Nations": [{"question": "Which is the largest country by area in Africa?", "options": {"A": "Nigeria", "B": "Egypt", "C": "Sudan", "D": "Algeria"}, "answer": "D"}, {"question": "What is the capital of Kenya?", "options": {"A": "Nairobi", "B": "Kampala", "C": "Dar es Salaam", "D": "Addis A<PERSON>ba"}, "answer": "A"}], "South American Facts": [{"question": "Which river is the longest in South America?", "options": {"A": "Amazon", "B": "Orinoco", "C": "Paraná", "D": "Magdalena"}, "answer": "A"}, {"question": "What is the capital of Argentina?", "options": {"A": "Buenos Aires", "B": "Lima", "C": "Santiago", "D": "Brasília"}, "answer": "A"}], "Time Zones": [{"question": "How many time zones are there in the world?", "options": {"A": "12", "B": "24", "C": "36", "D": "48"}, "answer": "B"}, {"question": "What does UTC stand for?", "options": {"A": "Universal Time Coordinated", "B": "Universal Time Code", "C": "United Time Code", "D": "Universal Technical Clock"}, "answer": "A"}], "Inventions & Inventors": [{"question": "Who invented the telephone?", "options": {"A": "<PERSON>", "B": "Thomas <PERSON>", "C": "<PERSON>", "D": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "answer": "A"}, {"question": "What did the <PERSON> brothers invent?", "options": {"A": "Telephone", "B": "Light Bulb", "C": "Airplane", "D": "Radio"}, "answer": "C"}], "Health & Medicine": [{"question": "What is the primary function of red blood cells?", "options": {"A": "Fight infections", "B": "Carry oxygen", "C": "Clot blood", "D": "Produce hormones"}, "answer": "B"}, {"question": "Which vitamin is produced when the skin is exposed to sunlight?", "options": {"A": "Vitamin A", "B": "Vitamin B12", "C": "Vitamin C", "D": "<PERSON><PERSON>"}, "answer": "D"}], "Human Body": [{"question": "What is the largest organ in the human body?", "options": {"A": "Heart", "B": "Liver", "C": "Skin", "D": "Brain"}, "answer": "C"}, {"question": "How many bones are in the adult human body?", "options": {"A": "206", "B": "201", "C": "210", "D": "196"}, "answer": "A"}], "Programming Basics": [{"question": "What symbol is used to start a comment in Python?", "options": {"A": "//", "B": "#", "C": "/*", "D": "<!--"}, "answer": "B"}, {"question": "Which of these is a data type in most programming languages?", "options": {"A": "Integer", "B": "Circle", "C": "Alphabet", "D": "Phrase"}, "answer": "A"}], "JavaScript Quiz": [{"question": "Which company developed JavaScript?", "options": {"A": "Microsoft", "B": "Netscape", "C": "Google", "D": "Apple"}, "answer": "B"}, {"question": "What keyword declares a variable in JavaScript?", "options": {"A": "var", "B": "int", "C": "dim", "D": "let"}, "answer": "A"}], "Python Programming": [{"question": "Which function is used to output text in Python?", "options": {"A": "echo()", "B": "print()", "C": "console.log()", "D": "output()"}, "answer": "B"}, {"question": "Which keyword is used to define a function in Python?", "options": {"A": "function", "B": "def", "C": "fun", "D": "define"}, "answer": "B"}], "Mobile Apps": [{"question": "Which mobile operating system is developed by Google?", "options": {"A": "iOS", "B": "Android", "C": "Windows Phone", "D": "BlackBerry OS"}, "answer": "B"}, {"question": "Which app is primarily used for instant messaging?", "options": {"A": "Instagram", "B": "WhatsApp", "C": "Spotify", "D": "Snapchat"}, "answer": "B"}], "Technology & Gadgets": [{"question": "Who founded Apple Inc.?", "options": {"A": "<PERSON>", "B": "<PERSON>", "C": "<PERSON><PERSON>", "D": "<PERSON>"}, "answer": "B"}, {"question": "What does 'USB' stand for?", "options": {"A": "Universal Serial Bus", "B": "United Serial Base", "C": "Universal Service Bus", "D": "United System Backup"}, "answer": "A"}], "AI & Machine Learning": [{"question": "What does 'AI' stand for?", "options": {"A": "Artificial Intelligence", "B": "Automated Interface", "C": "Algorithmic Input", "D": "Applied Integration"}, "answer": "A"}, {"question": "Which algorithm is commonly used for classification problems?", "options": {"A": "Linear Regression", "B": "Decision Trees", "C": "K - means", "D": "<PERSON><PERSON><PERSON>"}, "answer": "B"}]}