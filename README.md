# 📱 QuizMaster Pro - Interactive Quiz Application

A modern, mobile-friendly quiz application built with comprehensive question banks, supporting multiple knowledge categories and an engaging user experience.

## 🎯 Key Features

### Homepage (index.html)
- **Modern Design**: Gradient backgrounds and single-column card layout
- **Dynamic Borders**: Colorful gradient animated borders
- **Responsive Design**: Perfect mobile adaptation with hamburger menu
- **Real-time Countdown**: Dynamic countdown timers for each quiz
- **16 Featured Quiz Categories** (selected from 75+ available):
  - 🎬 Movie Trivia
  - 💻 Technology & Gadgets
  - 🚀 Space & Astronomy
  - 🌍 World Geography
  - 🇺🇸 U.S. History
  - 🦸 Marvel Universe
  - 📝 English Vocabulary
  - 📊 Mathematics
  - 🤖 AI & Machine Learning
  - 🎮 Video Games
  - ₿ Cryptocurrencies
  - 🎌 Anime & Manga
  - 👑 Famous People
  - 💾 Programming Basics
  - ⚕️ Health & Medicine
  - 🌱 Environmental Science

### Quiz Page (quiz.html)
- **Complete Quiz Flow**: 10 random questions per quiz
- **Real-time Timer**: 30-second countdown per question
- **Progress Display**: Visual progress bar
- **Instant Feedback**: Immediate correct/incorrect display
- **Score Statistics**: Detailed results and performance evaluation
- **Retry Option**: Support for restarting quizzes

## 🚀 How to Use

### Method 1: Direct File Access
1. Open `index.html` directly in your browser
2. Click any quiz's "Start Challenge" button to begin

### Method 2: HTTP Server (Recommended)
1. Start an HTTP server in the project directory:
   ```bash
   python -m http.server 8000
   ```
2. Visit in browser: `http://localhost:8000`
3. Enjoy the complete quiz experience

## 📁 File Structure

```
demo1/
├── index.html              # Homepage - Quiz categories list
├── quiz.html              # Quiz page - Interactive questions
├── about.html              # About page - Platform information
├── privacy.html            # Privacy Policy page
├── terms.html              # Terms of Service page
├── questions.js            # Quiz data processing logic
├── css/
│   ├── common.css          # Shared styles for all pages
│   ├── index.css           # Homepage specific styles
│   └── quiz.css            # Quiz page specific styles
├── images/
│   └── logo.png            # Application logo
├── data/
│   └── full_question_bank.json  # Complete question database
└── README.md               # Documentation
```

## 🎮 Quiz Rules

1. **Question Count**: 10 randomly selected questions per quiz
2. **Time Limit**: 15 seconds per question (updated for faster gameplay)
3. **Scoring System**:
   - Correct answer: 100+ points (with time bonus up to 175 points)
   - Time bonus: Based on remaining time (faster answers = more points)
   - Performance bonus: Additional points based on overall performance
   - Incorrect/timeout: 0 points
4. **Performance Evaluation & Levels**:
   - **Perfect Master** (100% + high speed): 👑 500 bonus points
   - **Excellent** (90%+ + good speed): 🏆 300 bonus points
   - **Great** (80%+ + decent speed): 🥇 200 bonus points
   - **Good** (70%+): 🥈 100 bonus points
   - **Fair** (50-69%): 🥉 50 bonus points
   - **Needs Practice** (<50%): 📚 25 bonus points
5. **User Levels** (based on total points):
   - **Grandmaster** (50,000+ pts): 👑
   - **Expert** (25,000+ pts): 🏆
   - **Advanced** (15,000+ pts): 🥇
   - **Intermediate** (8,000+ pts): 🥈
   - **Beginner** (3,000+ pts): 🥉
   - **Novice** (0-2,999 pts): 🌟

## 🛠️ Technical Features

- **Pure Frontend**: HTML + CSS + JavaScript
- **Responsive Design**: Single-column layout on all devices
- **Mobile-First**: Hamburger menu for mobile navigation
- **Modern UI**: Gradient colors, animations, card design
- **Data-Driven**: Dynamic content generation from updated JSON data structure
- **No Backend Required**: Runs directly in browser
- **Advanced Points System**: Local storage with level progression and performance bonuses
- **Enhanced Scoring**: Time-based bonuses and achievement system
- **Progressive Enhancement**: Works without JavaScript (basic functionality)

## 📊 Data Source

Quiz data sourced from comprehensive `./data/full_question_bank.json`, containing 1500+ questions across 75+ diverse categories including:

**Technology & Innovation:**
- AI & Machine Learning, Cryptocurrencies, Programming Basics
- Technology & Gadgets, Cybersecurity, Data Science

**Entertainment & Pop Culture:**
- Movie Trivia, Marvel Universe, Anime & Manga
- Video Games, Music, TV Shows, Celebrity Culture

**Science & Education:**
- Space & Astronomy, Health & Medicine, Environmental Science
- Mathematics, Physics, Chemistry, Biology

**History & Geography:**
- U.S. History, World Geography, Famous People
- Ancient Civilizations, World Wars, Cultural Studies

**And many more specialized categories covering diverse interests and knowledge areas.**

The JSON structure uses category names as keys with question arrays containing options as objects (A, B, C, D format).

## 🎨 Design Highlights

- **Colorful Gradient Borders**: Dynamic animated borders for each quiz card
- **Modern Gradient Buttons**: Contemporary gradient design for action buttons
- **Real-time Countdown**: Adds excitement and challenge
- **Instant Feedback**: Immediate result display after answer selection
- **Smooth Animations**: Hover effects, progress bars with fluid animations
- **Mobile-First Design**: Optimized for touch interfaces

## 🔧 Customization Guide

- **Modify Quiz Content**: Edit `./data/full_question_bank.json` file
- **Adjust Styles**: Modify CSS files in the `css/` directory
- **Update Quiz Logic**: Edit `questions.js` and JavaScript in HTML files
- **Add New Categories**: Update both JSON data and `quizData` array in `questions.js`

## 🌟 Additional Features

- **Advanced Points System**: Earn 100-175 points per correct answer with time bonuses
- **Performance Bonuses**: Extra points based on overall quiz performance (25-500 bonus points)
- **User Level System**: Progress through 6 levels from Novice to Grandmaster
- **Mobile-Optimized Navigation**: Hamburger menu with smooth animations
- **Single-Column Layout**: Consistent experience across all devices
- **Enhanced Timing**: 15-second questions for faster, more exciting gameplay
- **Local Storage**: Progress, points, and achievements saved locally
- **Multiple Pages**: About, Privacy Policy, and Terms of Service
- **Accessibility**: Keyboard navigation and screen reader friendly
- **Performance**: Optimized loading and smooth interactions

---

🎯 **Start Your Knowledge Challenge Journey Today!**
