# 📱 QuizMaster Pro - Interactive Quiz Application

A modern, mobile-friendly quiz application built with comprehensive question banks, supporting multiple knowledge categories and an engaging user experience.

## 🎯 Key Features

### Homepage (index.html)
- **Modern Design**: Gradient backgrounds and card-based layout
- **Dynamic Borders**: Colorful gradient animated borders
- **Responsive Design**: Perfect mobile adaptation
- **Real-time Countdown**: Dynamic countdown timers for each quiz
- **11 Quiz Categories**:
  - 🎬 Movies Mastery
  - 📺 TV Shows Expert
  - 📚 History Scholar
  - 🌍 Geography Explorer
  - 🔬 Science Genius
  - ⚛️ Physics Master
  - 🧪 Chemistry Champion
  - 🧬 Biology Expert
  - 📊 Math Wizard
  - 📝 Vocabulary Master
  - ✏️ Grammar Guru

### Quiz Page (quiz.html)
- **Complete Quiz Flow**: 10 random questions per quiz
- **Real-time Timer**: 30-second countdown per question
- **Progress Display**: Visual progress bar
- **Instant Feedback**: Immediate correct/incorrect display
- **Score Statistics**: Detailed results and performance evaluation
- **Retry Option**: Support for restarting quizzes

## 🚀 How to Use

### Method 1: Direct File Access
1. Open `index.html` directly in your browser
2. Click any quiz's "Start Challenge" button to begin

### Method 2: HTTP Server (Recommended)
1. Start an HTTP server in the project directory:
   ```bash
   python -m http.server 8000
   ```
2. Visit in browser: `http://localhost:8000`
3. Enjoy the complete quiz experience

## 📁 File Structure

```
demo1/
├── index.html              # Homepage - Quiz categories list
├── quiz.html              # Quiz page - Interactive questions
├── about.html              # About page - Platform information
├── privacy.html            # Privacy Policy page
├── terms.html              # Terms of Service page
├── questions.js            # Quiz data processing logic
├── css/
│   ├── common.css          # Shared styles for all pages
│   ├── index.css           # Homepage specific styles
│   └── quiz.css            # Quiz page specific styles
├── images/
│   └── logo.png            # Application logo
├── data/
│   └── full_question_bank.json  # Complete question database
└── README.md               # Documentation
```

## 🎮 Quiz Rules

1. **Question Count**: 10 randomly selected questions per quiz
2. **Time Limit**: 30 seconds per question
3. **Scoring System**:
   - Correct answer: 100+ points (with time bonus)
   - Incorrect/timeout: 0 points
4. **Performance Evaluation**:
   - 90%+: 🏆 Perfect Performance! You are a true expert!
   - 70-89%: 🎉 Excellent work! Keep it up!
   - 50-69%: 👍 Good job! There's room for improvement!
   - Below 50%: 💪 Keep trying! Practice makes perfect!

## 🛠️ Technical Features

- **Pure Frontend**: HTML + CSS + JavaScript
- **Responsive Design**: Adapts to all screen sizes
- **Modern UI**: Gradient colors, animations, card design
- **Data-Driven**: Dynamic content generation from JSON data
- **No Backend Required**: Runs directly in browser
- **Points System**: Local storage-based achievement tracking
- **Progressive Enhancement**: Works without JavaScript (basic functionality)

## 📊 Data Source

Quiz data sourced from `./data/full_question_bank.json`, containing 220+ questions across 11 categories, with approximately 20 questions per category.

## 🎨 Design Highlights

- **Colorful Gradient Borders**: Dynamic animated borders for each quiz card
- **Modern Gradient Buttons**: Contemporary gradient design for action buttons
- **Real-time Countdown**: Adds excitement and challenge
- **Instant Feedback**: Immediate result display after answer selection
- **Smooth Animations**: Hover effects, progress bars with fluid animations
- **Mobile-First Design**: Optimized for touch interfaces

## 🔧 Customization Guide

- **Modify Quiz Content**: Edit `./data/full_question_bank.json` file
- **Adjust Styles**: Modify CSS files in the `css/` directory
- **Update Quiz Logic**: Edit `questions.js` and JavaScript in HTML files
- **Add New Categories**: Update both JSON data and `quizData` array in `questions.js`

## 🌟 Additional Features

- **Points System**: Earn points for correct answers with time bonuses
- **Local Storage**: Progress and points saved locally
- **Multiple Pages**: About, Privacy Policy, and Terms of Service
- **Accessibility**: Keyboard navigation and screen reader friendly
- **Performance**: Optimized loading and smooth interactions

---

🎯 **Start Your Knowledge Challenge Journey Today!**
