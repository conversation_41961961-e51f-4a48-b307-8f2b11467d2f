# 📱 Quiz Master - 手机端答题应用

一个现代化的手机端答题应用，基于您提供的题库数据构建，支持多种类别的知识竞赛。

## 🎯 功能特点

### 首页 (index.html)
- **现代化设计**：渐变背景和卡片式布局
- **动态边框**：彩色渐变动画边框效果
- **响应式设计**：完美适配手机端
- **实时倒计时**：每个题库都有动态倒计时显示
- **11个题库类别**：
  - 🎬 Movies Quiz (电影知识)
  - 📺 TV Shows Quiz (电视剧知识)
  - 📚 History Quiz (历史知识)
  - 🌍 Geography Quiz (地理知识)
  - 🔬 General Science Quiz (综合科学)
  - ⚛️ Physics Quiz (物理学)
  - 🧪 Chemistry Quiz (化学)
  - 🧬 Biology Quiz (生物学)
  - 📊 Mathematics Quiz (数学)
  - 📝 English Vocabulary Quiz (英语词汇)
  - ✏️ Grammar Quiz (语法)

### 答题页面 (quiz.html)
- **完整答题流程**：10道随机题目
- **实时计时**：每题30秒倒计时
- **进度显示**：可视化进度条
- **即时反馈**：选择答案后立即显示正确/错误
- **成绩统计**：完成后显示详细成绩和评价
- **重新挑战**：支持重新开始答题

## 🚀 使用方法

### 方法一：直接打开文件
1. 直接在浏览器中打开 `index.html`
2. 点击任意题库的 "Play Now" 按钮开始答题

### 方法二：使用HTTP服务器（推荐）
1. 在项目目录下启动HTTP服务器：
   ```bash
   python -m http.server 8000
   ```
2. 在浏览器中访问：`http://localhost:8000`
3. 享受完整的答题体验

## 📁 文件结构

```
demo1/
├── index.html              # 首页 - 题库列表
├── quiz.html              # 答题页面
├── questions.js           # 题库数据处理逻辑
├── data/
│   └── full_question_bank.json  # 完整题库数据
└── README.md              # 说明文档
```

## 🎮 答题规则

1. **题目数量**：每次答题随机选择10道题
2. **答题时间**：每题限时30秒
3. **计分规则**：答对得1分，答错或超时得0分
4. **成绩评价**：
   - 90%以上：🏆 完美表现！你是真正的专家！
   - 70-89%：🎉 表现优秀！继续保持！
   - 50-69%：👍 不错的成绩！还有提升空间！
   - 50%以下：💪 继续努力！多练习会更好！

## 🛠️ 技术特点

- **纯前端实现**：HTML + CSS + JavaScript
- **响应式设计**：适配各种屏幕尺寸
- **现代化UI**：渐变色彩、动画效果、卡片设计
- **数据驱动**：基于JSON数据动态生成内容
- **无需后端**：可直接在浏览器中运行

## 📊 数据来源

题库数据来自 `./data/full_question_bank.json`，包含11个类别共220道题目，每个类别20道题。

## 🎨 设计亮点

- **彩色渐变边框**：每个题库卡片都有动态渐变边框
- **粉紫渐变按钮**：Play Now按钮采用现代化渐变设计
- **实时倒计时**：增加紧张感和挑战性
- **即时反馈**：选择答案后立即显示结果
- **流畅动画**：按钮悬停、进度条等都有平滑动画

## 🔧 自定义说明

如需修改题库内容，请编辑 `./data/full_question_bank.json` 文件。
如需调整样式，请修改各HTML文件中的CSS部分。
如需修改答题逻辑，请编辑 `questions.js` 和 `quiz.html` 中的JavaScript代码。

---

🎯 **开始你的知识挑战之旅吧！**
