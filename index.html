<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuizMaster Pro - Challenge Your Knowledge</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/index.css">

</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <img src="images/logo.png" alt="QuizMaster Pro">
                QuizMaster Pro
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="about.html">About</a></li>
                    <li><a href="privacy.html">Privacy Policy</a></li>
                    <li><a href="terms.html">Terms of Service</a></li>
                </ul>
            </nav>
            <div class="user-points" id="userPoints">
                <span class="points-icon">💎</span>
                <span id="pointsValue">0</span>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                ☰
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    <div class="mobile-menu" id="mobileMenu">
        <button class="mobile-menu-close" id="mobileMenuClose">✕</button>
        <ul class="mobile-nav-menu">
            <li><a href="index.html">🏠 Home</a></li>
            <li><a href="about.html">ℹ️ About</a></li>
            <li><a href="privacy.html">🔒 Privacy Policy</a></li>
            <li><a href="terms.html">📋 Terms of Service</a></li>
            <li>
                <div style="padding: 15px 20px; border-top: 1px solid #eee; margin-top: 20px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: #667eea; font-weight: bold;">
                        <span>💎</span>
                        <span id="mobilePointsValue">0</span>
                        <span>Points</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <h1>🎯 Challenge Your Knowledge</h1>
            <p>Test your expertise across multiple categories and earn points while having fun. Choose from our extensive collection of quizzes and compete with yourself to achieve the highest scores!</p>
        </div>

        <div class="quiz-container" id="quizContainer">
            <!-- Quiz cards will be dynamically generated by JavaScript -->
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="about.html">About Us</a>
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
                <a href="#contact">Contact</a>
            </div>
            <p class="footer-text">© 2024 QuizMaster Pro. All rights reserved. Challenge yourself, learn something new!</p>
        </div>
    </footer>

    <script src="js/common.js"></script>
    <script src="questions.js"></script>
    <script>
        // Page-specific functions (common functions are in common.js)

        // Countdown functionality
        function updateCountdowns() {
            document.querySelectorAll('.countdown').forEach(element => {
                const endTime = new Date(element.dataset.endTime).getTime();
                const now = new Date().getTime();
                const timeLeft = endTime - now;

                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                    element.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    element.textContent = '00:00:00';
                }
            });
        }

        // Render quiz cards
        function renderQuizCards() {
            const container = document.getElementById('quizContainer');
            container.innerHTML = '';

            quizData.forEach(quiz => {
                const card = document.createElement('div');
                card.className = 'quiz-card';

                // Calculate countdown end time (current time + random hours)
                const endTime = new Date();
                endTime.setHours(endTime.getHours() + Math.floor(Math.random() * 24) + 1);

                card.innerHTML = `
                    <div class="quiz-content">
                        <div class="quiz-image">
                            ${quiz.icon}
                        </div>
                        <div class="quiz-info">
                            <div class="quiz-title">${quiz.title}</div>
                            <div class="quiz-description">${quiz.description}</div>
                            <div class="quiz-status">
                                <span class="status-dot"></span>
                                <span>Live</span>
                            </div>
                            <div class="quiz-details">
                                <div class="detail-item">
                                    <span>💰</span>
                                    <span>Entry: Free</span>
                                </div>
                                <div class="detail-item">
                                    <span>🏆</span>
                                    <span>Reward: ${quiz.prize} pts</span>
                                </div>
                                <div class="detail-item">
                                    <span>⏰</span>
                                    <span class="countdown" data-end-time="${endTime.toISOString()}">00:00:00</span>
                                </div>
                            </div>
                            <button class="play-button" onclick="playQuiz('${quiz.id}')">
                                🎮 Start Challenge
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // Start quiz
        function playQuiz(quizId) {
            window.location.href = `quiz.html?quiz=${quizId}`;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            renderQuizCards();
            updateCountdowns();

            // Update countdown every second
            setInterval(updateCountdowns, 1000);

            // Welcome message for new users
            if (getUserPoints() === 0) {
                setTimeout(() => {
                    showNotification('Welcome to QuizMaster Pro! Start earning points by taking quizzes!', 'info');
                }, 1000);
            }
        });
    </script>
</body>
</html>
