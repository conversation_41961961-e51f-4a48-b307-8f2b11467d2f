<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Master - 题库挑战</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 15px;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .quiz-container {
            max-width: 400px;
            margin: 0 auto;
            height: calc(100vh - 150px);
            overflow-y: auto;
            padding-right: 5px;
        }

        .quiz-container::-webkit-scrollbar {
            width: 4px;
        }

        .quiz-container::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 2px;
        }

        .quiz-container::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }

        .quiz-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
            overflow: hidden;
        }

        .quiz-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            animation: gradientShift 3s ease infinite;
            z-index: -1;
            margin: -2px;
            border-radius: 16px;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .quiz-content {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .quiz-image {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            flex-shrink: 0;
        }

        .quiz-info {
            flex: 1;
        }

        .quiz-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .quiz-status {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 6px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #2ecc71;
        }

        .quiz-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 13px;
            color: #7f8c8d;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .countdown {
            font-weight: bold;
            color: #e74c3c;
            font-family: 'Courier New', monospace;
        }

        .play-button {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            margin-top: 12px;
            width: 100%;
        }

        .play-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .play-button:active {
            transform: translateY(0);
        }

        @media (max-width: 480px) {
            .quiz-container {
                max-width: 100%;
            }
            
            .quiz-content {
                gap: 12px;
            }
            
            .quiz-image {
                width: 70px;
                height: 70px;
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Quiz Master</h1>
        <p>挑战你的知识极限</p>
    </div>

    <div class="quiz-container" id="quizContainer">
        <!-- 题库卡片将通过JavaScript动态生成 -->
    </div>

    <script src="questions.js"></script>
    <script>
        // 倒计时功能
        function updateCountdowns() {
            document.querySelectorAll('.countdown').forEach(element => {
                const endTime = new Date(element.dataset.endTime).getTime();
                const now = new Date().getTime();
                const timeLeft = endTime - now;

                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    element.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    element.textContent = '00:00:00';
                }
            });
        }

        // 渲染题库卡片
        function renderQuizCards() {
            const container = document.getElementById('quizContainer');
            container.innerHTML = '';

            quizData.forEach(quiz => {
                const card = document.createElement('div');
                card.className = 'quiz-card';
                
                // 计算倒计时结束时间（当前时间 + 随机小时数）
                const endTime = new Date();
                endTime.setHours(endTime.getHours() + Math.floor(Math.random() * 24) + 1);
                
                card.innerHTML = `
                    <div class="quiz-content">
                        <div class="quiz-image">
                            ${quiz.icon}
                        </div>
                        <div class="quiz-info">
                            <div class="quiz-title">${quiz.title}</div>
                            <div class="quiz-status">
                                <span class="status-dot"></span>
                                <span>Live</span>
                            </div>
                            <div class="quiz-details">
                                <div class="detail-item">
                                    <span>💰</span>
                                    <span>Entry: Free</span>
                                </div>
                                <div class="detail-item">
                                    <span>🪙</span>
                                    <span>Winning: ${quiz.prize}</span>
                                </div>
                                <div class="detail-item">
                                    <span>⏰</span>
                                    <span class="countdown" data-end-time="${endTime.toISOString()}">00:00:00</span>
                                </div>
                            </div>
                            <button class="play-button" onclick="playQuiz('${quiz.id}')">
                                🎮 Play Now
                            </button>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // 开始游戏
        function playQuiz(quizId) {
            window.location.href = `quiz.html?quiz=${quizId}`;
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            renderQuizCards();
            updateCountdowns();
            
            // 每秒更新倒计时
            setInterval(updateCountdowns, 1000);
        });
    </script>
</body>
</html>
