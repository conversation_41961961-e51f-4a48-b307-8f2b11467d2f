// Load question bank data from JSON file
let fullQuestionBank = [];

// Quiz configuration data - based on actual JSON data
const quizData = [
    {
        id: 'movies',
        title: 'Movies Mastery',
        icon: '🎬',
        prize: '4500',
        category: 'Movies',
        description: 'Test your knowledge of cinema history, famous actors, directors, and iconic films from around the world.'
    },
    {
        id: 'tv-shows',
        title: 'TV Shows Expert',
        icon: '📺',
        prize: '4200',
        category: 'TV Shows',
        description: 'Challenge yourself with questions about popular TV series, characters, and memorable moments from television.'
    },
    {
        id: 'history',
        title: 'History Scholar',
        icon: '📚',
        prize: '5000',
        category: 'History',
        description: 'Journey through time and test your knowledge of historical events, figures, and civilizations.'
    },
    {
        id: 'geography',
        title: 'Geography Explorer',
        icon: '🌍',
        prize: '3800',
        category: 'Geography',
        description: 'Explore the world through questions about countries, capitals, landmarks, and natural wonders.'
    },
    {
        id: 'general-science',
        title: 'Science Genius',
        icon: '🔬',
        prize: '4800',
        category: 'General Science',
        description: 'Discover the wonders of science with questions covering various scientific disciplines and discoveries.'
    },
    {
        id: 'physics',
        title: 'Physics Master',
        icon: '⚛️',
        prize: '5200',
        category: 'Physics',
        description: 'Dive into the fundamental laws of nature and test your understanding of physics principles.'
    },
    {
        id: 'chemistry',
        title: 'Chemistry Champion',
        icon: '🧪',
        prize: '4900',
        category: 'Chemistry',
        description: 'Explore the world of elements, compounds, and chemical reactions in this challenging quiz.'
    },
    {
        id: 'biology',
        title: 'Biology Expert',
        icon: '🧬',
        prize: '4600',
        category: 'Biology',
        description: 'Test your knowledge of living organisms, ecosystems, and the mysteries of life itself.'
    },
    {
        id: 'mathematics',
        title: 'Math Wizard',
        icon: '📊',
        prize: '5500',
        category: 'Mathematics',
        description: 'Challenge your mathematical skills with problems involving numbers, logic, and mathematical concepts.'
    },
    {
        id: 'english-vocabulary',
        title: 'Vocabulary Master',
        icon: '📝',
        prize: '3500',
        category: 'English Vocabulary',
        description: 'Expand your English vocabulary and test your knowledge of word meanings and usage.'
    },
    {
        id: 'grammar',
        title: 'Grammar Guru',
        icon: '✏️',
        prize: '3200',
        category: 'Grammar',
        description: 'Master the rules of English grammar and test your understanding of language structure.'
    }
];

// Asynchronously load question bank data
async function loadQuestionBank() {
    try {
        const response = await fetch('./data/full_question_bank.json');
        fullQuestionBank = await response.json();
        console.log('Question bank loaded successfully:', fullQuestionBank.length, 'categories');
    } catch (error) {
        console.error('Failed to load question bank:', error);
        // Use empty array if loading fails
        fullQuestionBank = [];
    }
}

// Get questions for specified quiz
function getQuizQuestions(quizId) {
    const quiz = quizData.find(q => q.id === quizId);
    if (!quiz) return [];

    const categoryData = fullQuestionBank.find(cat => cat.category === quiz.category);
    if (!categoryData) return [];

    // Convert data format for quiz page compatibility
    return categoryData.questions.map((q, index) => ({
        id: index + 1,
        question: q.question,
        options: q.options,
        correct: q.options.indexOf(q.answer),
        explanation: `The correct answer is: ${q.answer}`
    }));
}

// Get quiz information
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}

// Get random questions for quiz
function getRandomQuestions(quizId, count = 10) {
    const allQuestions = getQuizQuestions(quizId);
    if (allQuestions.length === 0) return [];

    // Randomly select specified number of questions
    const shuffled = [...allQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, allQuestions.length));
}

// Initialize data when page loads
if (typeof window !== 'undefined') {
    // Auto-load data in browser environment
    loadQuestionBank();
}
