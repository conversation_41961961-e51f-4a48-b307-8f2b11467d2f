// Load question bank data from JSON file
let fullQuestionBank = [];

// Quiz configuration data - based on updated JSON data with 75+ categories
const quizData = [
    {
        id: 'movie-trivia',
        title: 'Movie Trivia',
        icon: '🎬',
        prize: '4500',
        category: 'Movie Trivia',
        description: 'Test your knowledge of cinema history, famous actors, directors, and iconic films from around the world.'
    },
    {
        id: 'technology-gadgets',
        title: 'Technology & Gadgets',
        icon: '💻',
        prize: '5200',
        category: 'Technology & Gadgets',
        description: 'Stay up-to-date with the latest in technology, gadgets, and digital innovations.'
    },
    {
        id: 'space-astronomy',
        title: 'Space & Astronomy',
        icon: '🚀',
        prize: '4800',
        category: 'Space & Astronomy',
        description: 'Discover the wonders of the universe with questions about planets, stars, and space exploration.'
    },
    {
        id: 'world-geography',
        title: 'World Geography',
        icon: '🌍',
        prize: '4200',
        category: 'World Geography',
        description: 'Explore the world through questions about countries, capitals, landmarks, and natural wonders.'
    },
    {
        id: 'us-history',
        title: 'U.S. History',
        icon: '🇺🇸',
        prize: '5000',
        category: 'U.S. History',
        description: 'Journey through American history and test your knowledge of key events, figures, and milestones.'
    },
    {
        id: 'marvel-universe',
        title: 'Marvel Universe',
        icon: '🦸',
        prize: '4600',
        category: 'Marvel Universe',
        description: 'Test your knowledge of Marvel superheroes, villains, and the expansive Marvel Cinematic Universe.'
    },
    {
        id: 'english-vocabulary',
        title: 'English Vocabulary',
        icon: '📝',
        prize: '3800',
        category: 'English Vocabulary',
        description: 'Expand your English vocabulary and test your knowledge of word meanings and usage.'
    },
    {
        id: 'mathematics',
        title: 'Mathematics',
        icon: '📊',
        prize: '5500',
        category: 'Mathematics',
        description: 'Challenge your mathematical skills with problems involving algebra, geometry, and calculus.'
    },
    {
        id: 'ai-machine-learning',
        title: 'AI & Machine Learning',
        icon: '🤖',
        prize: '5800',
        category: 'AI & Machine Learning',
        description: 'Explore the cutting-edge world of artificial intelligence and machine learning technologies.'
    },
    {
        id: 'video-games',
        title: 'Video Games',
        icon: '🎮',
        prize: '4400',
        category: 'Video Games',
        description: 'Test your knowledge of gaming history, popular titles, and gaming culture.'
    },
    {
        id: 'cryptocurrencies',
        title: 'Cryptocurrencies',
        icon: '₿',
        prize: '5300',
        category: 'Cryptocurrencies',
        description: 'Dive into the world of digital currencies, blockchain technology, and crypto trading.'
    },
    {
        id: 'anime-manga',
        title: 'Anime & Manga',
        icon: '🎌',
        prize: '4100',
        category: 'Anime & Manga',
        description: 'Challenge your knowledge of Japanese animation, manga series, and anime culture.'
    },
    {
        id: 'famous-people',
        title: 'Famous People',
        icon: '👑',
        prize: '4000',
        category: 'Famous People',
        description: 'Test your knowledge of historical figures, celebrities, and influential personalities.'
    },
    {
        id: 'programming-basics',
        title: 'Programming Basics',
        icon: '💾',
        prize: '5100',
        category: 'Programming Basics',
        description: 'Test your understanding of programming concepts, languages, and software development.'
    },
    {
        id: 'health-medicine',
        title: 'Health & Medicine',
        icon: '⚕️',
        prize: '4700',
        category: 'Health & Medicine',
        description: 'Explore medical knowledge, health facts, and wellness information.'
    },
    {
        id: 'environmental-science',
        title: 'Environmental Science',
        icon: '🌱',
        prize: '4300',
        category: 'Environmental Science',
        description: 'Learn about ecology, climate change, and environmental conservation.'
    }
];

// Asynchronously load question bank data
async function loadQuestionBank() {
    try {
        const response = await fetch('./data/full_question_bank.json');
        fullQuestionBank = await response.json();
        console.log('Question bank loaded successfully:', fullQuestionBank.length, 'categories');
    } catch (error) {
        console.error('Failed to load question bank:', error);
        // Use empty array if loading fails
        fullQuestionBank = [];
    }
}

// Get questions for specified quiz
function getQuizQuestions(quizId) {
    const quiz = quizData.find(q => q.id === quizId);
    if (!quiz) return [];

    // The new JSON structure has categories as keys
    const categoryQuestions = fullQuestionBank[quiz.category];
    if (!categoryQuestions) return [];

    // Convert data format for quiz page compatibility
    return categoryQuestions.map((q, index) => {
        // Convert options object to array
        const optionsArray = Object.values(q.options);
        const correctIndex = Object.keys(q.options).indexOf(q.answer);

        return {
            id: index + 1,
            question: q.question,
            options: optionsArray,
            correct: correctIndex,
            explanation: `The correct answer is: ${q.options[q.answer]}`
        };
    });
}

// Get quiz information
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}

// Get random questions for quiz
function getRandomQuestions(quizId, count = 10) {
    const allQuestions = getQuizQuestions(quizId);
    if (allQuestions.length === 0) return [];

    // Randomly select specified number of questions
    const shuffled = [...allQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, allQuestions.length));
}

// Initialize data when page loads
if (typeof window !== 'undefined') {
    // Auto-load data in browser environment
    loadQuestionBank();
}
