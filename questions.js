// 从JSON文件加载题库数据
let fullQuestionBank = [];

// 题库配置数据 - 根据实际JSON数据生成
const quizData = [
    {
        id: 'movies',
        title: 'Movies Quiz',
        icon: '🎬',
        prize: '4500',
        category: 'Movies',
        description: '电影知识大挑战，测试你的影视文化素养'
    },
    {
        id: 'tv-shows',
        title: 'TV Shows Quiz',
        icon: '📺',
        prize: '4200',
        category: 'TV Shows',
        description: '电视剧知识竞赛，挑战你的追剧功力'
    },
    {
        id: 'history',
        title: 'History Quiz',
        icon: '📚',
        prize: '5000',
        category: 'History',
        description: '历史知识大考验，穿越时空的智慧挑战'
    },
    {
        id: 'geography',
        title: 'Geography Quiz',
        icon: '🌍',
        prize: '3800',
        category: 'Geography',
        description: '地理知识竞赛，探索世界的奥秘'
    },
    {
        id: 'general-science',
        title: 'General Science Quiz',
        icon: '🔬',
        prize: '4800',
        category: 'General Science',
        description: '综合科学知识挑战，探索科学的无限魅力'
    },
    {
        id: 'physics',
        title: 'Physics Quiz',
        icon: '⚛️',
        prize: '5200',
        category: 'Physics',
        description: '物理学知识竞赛，挑战物理定律的奥秘'
    },
    {
        id: 'chemistry',
        title: 'Chemistry Quiz',
        icon: '🧪',
        prize: '4900',
        category: 'Chemistry',
        description: '化学知识大挑战，探索元素的神奇世界'
    },
    {
        id: 'biology',
        title: 'Biology Quiz',
        icon: '🧬',
        prize: '4600',
        category: 'Biology',
        description: '生物学知识竞赛，探索生命的奥秘'
    },
    {
        id: 'mathematics',
        title: 'Mathematics Quiz',
        icon: '📊',
        prize: '5500',
        category: 'Mathematics',
        description: '数学知识挑战，数字与逻辑的完美结合'
    },
    {
        id: 'english-vocabulary',
        title: 'English Vocabulary Quiz',
        icon: '📝',
        prize: '3500',
        category: 'English Vocabulary',
        description: '英语词汇大挑战，提升你的语言能力'
    },
    {
        id: 'grammar',
        title: 'Grammar Quiz',
        icon: '✏️',
        prize: '3200',
        category: 'Grammar',
        description: '语法知识竞赛，掌握语言的精髓'
    }
];

// 异步加载题库数据
async function loadQuestionBank() {
    try {
        const response = await fetch('./data/full_question_bank.json');
        fullQuestionBank = await response.json();
        console.log('题库数据加载成功:', fullQuestionBank.length, '个类别');
    } catch (error) {
        console.error('加载题库数据失败:', error);
        // 如果加载失败，使用空数组
        fullQuestionBank = [];
    }
}

// 获取指定题库的题目
function getQuizQuestions(quizId) {
    const quiz = quizData.find(q => q.id === quizId);
    if (!quiz) return [];

    const categoryData = fullQuestionBank.find(cat => cat.category === quiz.category);
    if (!categoryData) return [];

    // 转换数据格式以适配答题页面
    return categoryData.questions.map((q, index) => ({
        id: index + 1,
        question: q.question,
        options: q.options,
        correct: q.options.indexOf(q.answer),
        explanation: `正确答案是: ${q.answer}`
    }));
}

// 获取题库信息
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}

// 获取随机题目（用于答题）
function getRandomQuestions(quizId, count = 10) {
    const allQuestions = getQuizQuestions(quizId);
    if (allQuestions.length === 0) return [];

    // 随机选择指定数量的题目
    const shuffled = [...allQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, allQuestions.length));
}

// 页面加载时初始化数据
if (typeof window !== 'undefined') {
    // 浏览器环境下自动加载数据
    loadQuestionBank();
}
