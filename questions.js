// 题库数据
const quizData = [
    {
        id: 'business',
        title: 'Business Quiz',
        icon: '💼',
        prize: '4000',
        category: 'business',
        description: '测试你的商业知识和管理技能'
    },
    {
        id: 'science',
        title: 'Science Quiz',
        icon: '🔬',
        prize: '3500',
        category: 'science',
        description: '探索科学的奥秘，挑战你的科学素养'
    },
    {
        id: 'history',
        title: 'History Quiz',
        icon: '📚',
        prize: '3000',
        category: 'history',
        description: '穿越时空，回顾历史的精彩瞬间'
    },
    {
        id: 'technology',
        title: 'Technology Quiz',
        icon: '💻',
        prize: '5000',
        category: 'technology',
        description: '科技前沿知识，IT达人的挑战'
    },
    {
        id: 'sports',
        title: 'Sports Quiz',
        icon: '⚽',
        prize: '2800',
        category: 'sports',
        description: '体育竞技知识大比拼'
    },
    {
        id: 'geography',
        title: 'Geography Quiz',
        icon: '🌍',
        prize: '3200',
        category: 'geography',
        description: '环游世界，测试你的地理知识'
    },
    {
        id: 'literature',
        title: 'Literature Quiz',
        icon: '📖',
        prize: '2500',
        category: 'literature',
        description: '文学经典，诗词歌赋知识挑战'
    },
    {
        id: 'music',
        title: 'Music Quiz',
        icon: '🎵',
        prize: '2200',
        category: 'music',
        description: '音乐知识大考验，旋律与节拍的世界'
    },
    {
        id: 'movies',
        title: 'Movies Quiz',
        icon: '🎬',
        prize: '2600',
        category: 'movies',
        description: '电影百科全书，影迷必备挑战'
    },
    {
        id: 'food',
        title: 'Food & Cooking Quiz',
        icon: '🍳',
        prize: '1800',
        category: 'food',
        description: '美食文化与烹饪技巧知识竞赛'
    },
    {
        id: 'art',
        title: 'Art Quiz',
        icon: '🎨',
        prize: '2400',
        category: 'art',
        description: '艺术鉴赏与创作知识挑战'
    },
    {
        id: 'nature',
        title: 'Nature Quiz',
        icon: '🌿',
        prize: '2100',
        category: 'nature',
        description: '自然生态与环保知识测试'
    }
];

// 示例题目数据结构（可用于后续开发quiz.html页面）
const sampleQuestions = {
    business: [
        {
            id: 1,
            question: "什么是市场营销的4P理论？",
            options: [
                "Product, Price, Place, Promotion",
                "People, Process, Physical, Performance",
                "Plan, Prepare, Present, Perform",
                "Profit, Product, Price, People"
            ],
            correct: 0,
            explanation: "4P理论包括产品(Product)、价格(Price)、渠道(Place)和促销(Promotion)。"
        },
        {
            id: 2,
            question: "SWOT分析中的'T'代表什么？",
            options: [
                "Technology",
                "Threats",
                "Trends",
                "Time"
            ],
            correct: 1,
            explanation: "SWOT分析中T代表威胁(Threats)，其他分别是优势(Strengths)、劣势(Weaknesses)、机会(Opportunities)。"
        }
    ],
    science: [
        {
            id: 1,
            question: "光在真空中的传播速度约为多少？",
            options: [
                "300,000 km/s",
                "299,792,458 m/s",
                "3×10^8 m/s",
                "以上都正确"
            ],
            correct: 3,
            explanation: "光在真空中的精确速度是299,792,458米/秒，约等于3×10^8米/秒或300,000公里/秒。"
        }
    ],
    technology: [
        {
            id: 1,
            question: "以下哪个不是编程语言？",
            options: [
                "Python",
                "JavaScript",
                "HTML",
                "Java"
            ],
            correct: 2,
            explanation: "HTML是标记语言，用于创建网页结构，不是编程语言。"
        }
    ]
};

// 获取指定题库的题目
function getQuizQuestions(quizId) {
    return sampleQuestions[quizId] || [];
}

// 获取题库信息
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}
