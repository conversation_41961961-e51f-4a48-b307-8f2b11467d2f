// Load question bank data from JSON file
let fullQuestionBank = [];

// Quiz configuration data - based on actual JSON data
const quizData = [
    {
        id: 'movie-trivia',
        title: 'Movie Trivia',
        icon: '🎬',
        prize: '4500',
        category: 'Movie Trivia',
        description: 'Test your knowledge of cinema history, famous actors, directors, and iconic films from around the world.'
    },
    {
        id: 'zodiac-signs',
        title: 'Zodiac Signs',
        icon: '♈',
        prize: '3200',
        category: 'Zodiac Signs',
        description: 'Explore the mysteries of astrology and test your knowledge of zodiac signs and their characteristics.'
    },
    {
        id: 'law-legal',
        title: 'Law & Legal Knowledge',
        icon: '⚖️',
        prize: '5500',
        category: 'Law and Legal Knowledge',
        description: 'Challenge yourself with questions about legal principles, constitutional law, and legal terminology.'
    },
    {
        id: 'us-history',
        title: 'U.S. History',
        icon: '🇺🇸',
        prize: '5000',
        category: 'U.S. History',
        description: 'Journey through American history and test your knowledge of key events, figures, and milestones.'
    },
    {
        id: 'world-geography',
        title: 'World Geography',
        icon: '🌍',
        prize: '4200',
        category: 'World Geography',
        description: 'Explore the world through questions about countries, capitals, landmarks, and natural wonders.'
    },
    {
        id: 'space-astronomy',
        title: 'Space & Astronomy',
        icon: '🚀',
        prize: '4800',
        category: 'Space & Astronomy',
        description: 'Discover the wonders of the universe with questions about planets, stars, and space exploration.'
    },
    {
        id: 'famous-people',
        title: 'Famous People',
        icon: '👑',
        prize: '4000',
        category: 'Famous People',
        description: 'Test your knowledge of historical figures, celebrities, and influential personalities throughout history.'
    },
    {
        id: 'us-presidents',
        title: 'U.S. Presidents',
        icon: '🏛️',
        prize: '4600',
        category: 'U.S. Presidents',
        description: 'Challenge yourself with questions about American presidents and their contributions to history.'
    },
    {
        id: 'technology-gadgets',
        title: 'Technology & Gadgets',
        icon: '💻',
        prize: '4400',
        category: 'Technology & Gadgets',
        description: 'Stay up-to-date with the latest in technology, gadgets, and digital innovations.'
    },
    {
        id: 'english-vocabulary',
        title: 'English Vocabulary',
        icon: '📝',
        prize: '3500',
        category: 'English Vocabulary',
        description: 'Expand your English vocabulary and test your knowledge of word meanings and usage.'
    },
    {
        id: 'mathematics',
        title: 'Mathematics',
        icon: '📊',
        prize: '5200',
        category: 'Mathematics',
        description: 'Challenge your mathematical skills with problems involving numbers, logic, and mathematical concepts.'
    },
    {
        id: 'science-basics',
        title: 'Science Fundamentals',
        icon: '🔬',
        prize: '4700',
        category: 'Physics Fundamentals',
        description: 'Dive into the fundamental laws of nature and test your understanding of scientific principles.'
    }
];

// Asynchronously load question bank data
async function loadQuestionBank() {
    try {
        const response = await fetch('./data/full_question_bank.json');
        fullQuestionBank = await response.json();
        console.log('Question bank loaded successfully:', fullQuestionBank.length, 'categories');
    } catch (error) {
        console.error('Failed to load question bank:', error);
        // Use empty array if loading fails
        fullQuestionBank = [];
    }
}

// Get questions for specified quiz
function getQuizQuestions(quizId) {
    const quiz = quizData.find(q => q.id === quizId);
    if (!quiz) return [];

    // The new JSON structure has categories as keys
    const categoryQuestions = fullQuestionBank[quiz.category];
    if (!categoryQuestions) return [];

    // Convert data format for quiz page compatibility
    return categoryQuestions.map((q, index) => {
        // Convert options object to array
        const optionsArray = Object.values(q.options);
        const correctIndex = Object.keys(q.options).indexOf(q.answer);

        return {
            id: index + 1,
            question: q.question,
            options: optionsArray,
            correct: correctIndex,
            explanation: `The correct answer is: ${q.options[q.answer]}`
        };
    });
}

// Get quiz information
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}

// Get random questions for quiz
function getRandomQuestions(quizId, count = 10) {
    const allQuestions = getQuizQuestions(quizId);
    if (allQuestions.length === 0) return [];

    // Randomly select specified number of questions
    const shuffled = [...allQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, allQuestions.length));
}

// Initialize data when page loads
if (typeof window !== 'undefined') {
    // Auto-load data in browser environment
    loadQuestionBank();
}
